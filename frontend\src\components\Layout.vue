<template>
  <div class="full-height">
    <el-container class="layout-container" style="height: 100%; display: flex; flex-direction: column">
      <!-- 顶部导航栏 -->
      <el-header class="header" style="height: 64px; flex-shrink: 0">
      <div class="header-left">
        <h1 class="logo" style="
          background: linear-gradient(135deg, #4361ee 0%, #3a0ca3 100%);
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
          text-shadow: 
            0 2px 4px rgba(0,0,0,0.1),
            0 4px 8px rgba(58,12,163,0.2);
          display: inline-block;
          transition: all 0.3s ease;
          transform-origin: left center;
        ">ThunderHub</h1>
      </div>
      <div class="header-right" v-if="$route.meta.requiresAuth">
        <el-dropdown>
          <span class="user-info">
            <el-avatar :size="30" :style="{
              backgroundColor: '#4f46e5',
              color: 'white',
              fontSize: '14px',
              fontWeight: 'bold',
              boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
            }">
              {{ authStore.user?.username?.charAt(0)?.toUpperCase() || 'U' }}
            </el-avatar>
            <span class="username">{{ authStore.user?.username || '用户' }}</span>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="handleLogout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </el-header>

    <!-- 主内容区 -->
    <el-container style="flex: 1; display: flex">
      <!-- 侧边栏菜单 -->
      <el-aside :width="isCollapse ? '64px' : '220px'" style="flex-shrink: 0; position: relative; overflow-x: hidden">
        <div
          @click="isCollapse = !isCollapse"
          class="collapse-toggle-button"
          style="line-height: 3px; text-align: center"
        >
          {{ isCollapse ? '›››\n ' : '‹‹‹\n ' }}
        </div>
        <el-menu
          router
          :default-active="$route.path"
          class="side-menu"
          background-color="#f8f9fa"
          text-color="#495057"
          active-text-color="#4361ee"
          :collapse="isCollapse"
          :unique-opened="false"
        >
          <template v-for="item in menuItems" :key="item.title">
            <!-- 有子菜单的项 -->
            <el-sub-menu v-if="item.children?.length" :index="item.title">
              <template #title>
                <el-icon>
                  <component :is="ElementPlusIcons[item.icon as keyof typeof ElementPlusIcons]" />
                </el-icon>
                <span>{{ item.title }}</span>
              </template>
              <el-menu-item
                v-for="child in item.children"
                :key="child.title"
                :index="child.to"
              >
                <el-icon>
                  <component :is="ElementPlusIcons[child.icon as keyof typeof ElementPlusIcons]" />
                </el-icon>
                <span>{{ child.title }}</span>
              </el-menu-item>
            </el-sub-menu>
            
            <!-- 没有子菜单的项 -->
            <el-menu-item v-else :index="item.to">
              <el-icon>
                <component :is="ElementPlusIcons[item.icon as keyof typeof ElementPlusIcons]" />
                </el-icon>
              <span>{{ item.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>

      <!-- 主内容区 -->
      <el-main style="flex: 1 1 auto; padding: 20px; overflow: hidden; min-height: 0">
        <router-view />
      </el-main>
    </el-container>
      </el-container>
    </div>
  </template>

<script lang="ts" setup>
import { computed, onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessage } from 'element-plus'
import * as ElementPlusIcons from '@element-plus/icons-vue'

const isCollapse = ref(false)

// 动态生成菜单
const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

onMounted(() => {
  console.log('Router instance:', router)
  console.log('Current route:', route)
})

const handleLogout = async () => {
  try {
    await authStore.logoutUser()
    ElMessage.success('已退出登录')
    router.push('/login')
  } catch (error) {
    ElMessage.error('退出登录失败')
    console.error('Logout error:', error)
  }
}

const menuItems = computed(() => {
  const routes = router.getRoutes()
  console.log('All routes:', routes)
  
  const topLevelMenus = []
  const childMenus = new Map()

  // 先收集所有菜单项
  routes.forEach(route => {
    if (route.meta?.menuItem && route.path !== '/') {
      const menuItem = {
        title: route.meta.title,
        icon: route.meta.icon,
        to: route.path,
        children: []
      }

      // 处理子菜单
      if (route.path.includes('/') && route.path.split('/').length > 2) {
        const parentPath = '/' + route.path.split('/')[1]
        if (!childMenus.has(parentPath)) {
          childMenus.set(parentPath, [])
        }
        childMenus.get(parentPath).push(menuItem)
      } else {
        // 顶级菜单
        topLevelMenus.push(menuItem)
      }
    }
  })

  // 构建菜单树
  const buildMenuTree = (menus) => {
    return menus.map(menu => {
      if (childMenus.has(menu.to)) {
        menu.children = buildMenuTree(childMenus.get(menu.to))
      }
      return menu
    })
  }

  const result = buildMenuTree(topLevelMenus)
  console.log('Final menu structure:', result)
  return result
})
</script>

<style scoped>
/* 全局样式重置 */
:deep() {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }
  
  html, body {
    height: 100%;
    overflow: hidden;
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB',
      'Microsoft YaHei', Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  height: 64px;
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
  position: sticky;
  top: 0;
}

.logo {
  color: #4361ee;
  font-size: 1.5rem;
  margin: 0;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.username {
  font-weight: 600;
  color: #1e293b;
  transition: all 0.2s ease;
  margin-left: 8px;
}

.user-info:hover .username {
  color: #3b82f6;
  transform: translateX(2px);
}

.collapse-toggle-button {
  position: absolute;
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  width: 24px;
  height: 24px;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 24px;
  font-weight: bolder;
  color: #409eff;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(64,158,255,0.3);
  user-select: none;
  white-space: pre;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-toggle-button::after {
  content: attr(data-symbol);
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  line-height: 1;
  white-space: pre;
}

.side-menu {
  height: 100%;
  border-right: none;
  box-shadow: 1px 0 4px rgba(0, 0, 0, 0.1);
  background-color: #f8f9fa;
  transition: width 0.3s ease;
  width: 100%;
  margin-right: -1px;
}

.full-height {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  width: 100%;
}

.side-menu :deep(.el-menu-item),
.side-menu :deep(.el-sub-menu__title) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.side-menu::-webkit-scrollbar {
  width: 6px;
}

.side-menu::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.el-menu-item {
  margin: 4px 8px;
  border-radius: 6px;
}

.el-menu-item.is-active {
  background-color: #e6f0ff !important;
}

.el-sub-menu__title {
  margin: 4px 8px;
  border-radius: 6px;
}
</style>