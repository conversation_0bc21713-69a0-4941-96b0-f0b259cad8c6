<template>
  <div class="task-config">
    <h2>配置任务</h2>

    <el-form :model="config" label-width="120px">
      <!-- 内容详情配置 -->
      <el-form-item label="标题模板">
        <el-input v-model="config.titleTemplate" placeholder="例如: {filename} - 精彩视频" />
      </el-form-item>

      <el-form-item label="描述">
        <el-input type="textarea" v-model="config.description" rows="4" />
      </el-form-item>

      <!-- 标签配置 -->
      <el-form-item label="标签">
        <el-tag
          v-for="tag in config.tags"
          :key="tag"
          closable
          @close="removeTag(tag)"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
        <el-input
          v-if="inputTagVisible"
          ref="tagInput"
          v-model="inputTagValue"
          size="small"
          class="tag-input"
          @keyup.enter="addTag"
          @blur="addTag"
        />
        <el-button v-else size="small" @click="showTagInput">+ 添加标签</el-button>
      </el-form-item>

      <!-- 内容类型选择 -->
      <el-form-item label="内容类型">
        <el-select v-model="config.contentType" placeholder="选择内容类型">
          <el-option label="普通视频" value="video" />
          <el-option label="短视频" value="shorts" />
          <el-option label="直播" value="live" />
          <el-option label="帖子" value="post" />
        </el-select>
      </el-form-item>

      <!-- 发布策略 -->
      <el-form-item label="隐私设置">
        <el-radio-group v-model="config.privacyStatus">
          <el-radio label="public">公开</el-radio>
          <el-radio label="unlisted">不公开</el-radio>
          <el-radio label="private">私有</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 发布时间设置 -->
      <el-form-item label="发布时间">
        <el-radio-group v-model="config.scheduleType">
          <el-radio label="immediate">立即发布</el-radio>
          <el-radio label="scheduled">定时发布</el-radio>
        </el-radio-group>
        <div v-if="config.scheduleType === 'scheduled'" class="schedule-time">
          <el-date-picker
            v-model="config.scheduleTime"
            type="datetime"
            placeholder="选择发布时间"
          />
        </div>
      </el-form-item>

      <!-- 高级设置 -->
      <el-collapse>
        <el-collapse-item title="高级设置" name="advanced">
          <el-form-item label="分类">
            <el-select v-model="config.category" placeholder="选择分类">
              <el-option
                v-for="item in categories"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="语言">
            <el-select v-model="config.language" placeholder="选择语言">
              <el-option label="中文" value="zh" />
              <el-option label="英文" value="en" />
              <el-option label="日文" value="ja" />
              <el-option label="韩文" value="ko" />
            </el-select>
          </el-form-item>

          <el-form-item label="许可证">
            <el-select v-model="config.license" placeholder="选择许可证">
              <el-option label="标准 YouTube 许可证" value="youtube" />
              <el-option label="知识共享" value="creativeCommon" />
            </el-select>
          </el-form-item>
        </el-collapse-item>
      </el-collapse>

      <!-- 音乐库选择（仅短视频显示） -->
      <div v-if="config.contentType === 'shorts'">
        <MusicLibrary
          :platform="'youtube'"
          @music-selected="handleMusicSelected"
        />
      </div>

      <!-- 重要提示 -->
      <el-form-item>
        <el-alert
          title="⚠️ 重要提示"
          type="warning"
          description="请务必点击'保存配置并继续'按钮来保存您的配置（特别是内容类型设置），否则配置将丢失！"
          :closable="false"
          style="margin-bottom: 20px;"
        />
      </el-form-item>

      <!-- 按钮区域 -->
      <el-form-item>
        <div class="button-group">
          <el-button
            type="primary"
            @click="saveConfigAndNext"
            :loading="loading"
            size="large"
            style="font-weight: bold; padding: 12px 30px;"
          >
            💾 保存配置并继续下一步
          </el-button>
          <el-button
            @click="saveConfig"
            :loading="loading"
            style="margin-left: 15px;"
          >
            仅保存配置
          </el-button>
        </div>

        <div class="help-text" style="margin-top: 15px;">
          <p style="color: #909399; font-size: 13px; line-height: 1.5;">
            💡 <strong>使用提示：</strong><br>
            • 选择了"短视频"类型后，请务必点击"保存配置并继续下一步"<br>
            • "仅保存配置"不会跳转到下一步，适合临时保存<br>
            • 当前内容类型：<span style="color: #409EFF; font-weight: bold;">{{ config.contentType === 'shorts' ? '短视频' : '普通视频' }}</span>
          </p>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import MusicLibrary from './MusicLibrary.vue'

const props = defineProps({
  taskId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['config-completed'])

// 标签输入相关
const inputTagVisible = ref(false)
const inputTagValue = ref('')
const tagInput = ref(null)

// 加载状态
const loading = ref(false)

// 配置数据
const config = reactive({
  titleTemplate: '{filename} - 精彩视频',
  description: '',
  tags: [] as string[],
  contentType: 'video',  // 新增内容类型字段，默认为普通视频
  privacyStatus: 'public',
  scheduleType: 'immediate',
  scheduleTime: '',
  category: '',
  language: 'zh',
  license: 'youtube',
  selectedMusic: [] as any[]  // 新增选中的音乐列表
})

// 分类选项
const categories = [
  { value: 'entertainment', label: '娱乐' },
  { value: 'music', label: '音乐' },
  { value: 'sports', label: '体育' },
  { value: 'gaming', label: '游戏' },
  { value: 'education', label: '教育' }
]

// 初始化加载
onMounted(async () => {
  await fetchTaskConfig()
})

// 获取任务配置
const fetchTaskConfig = async () => {
  try {
    loading.value = true
    // 这里应该调用API获取任务配置
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))

    // 模拟数据 - 不覆盖contentType，保持用户选择
    const defaultConfig = {
      titleTemplate: '{filename} - 精彩视频',
      description: '这是一个自动生成的描述，请根据实际内容修改。',
      tags: ['视频', '精彩', 'ThunderHub'],
      // 注意：不设置contentType，保持用户的选择
      privacyStatus: 'public',
      scheduleType: 'immediate',
      scheduleTime: '',
      category: 'entertainment',
      language: 'zh',
      license: 'youtube'
    }

    // 只更新不存在的字段，保持用户已有的选择
    Object.keys(defaultConfig).forEach(key => {
      if (config[key] === undefined || config[key] === '') {
        config[key] = defaultConfig[key]
      }
    })

    console.log('配置加载完成，当前contentType:', config.contentType)
    ElMessage.success('配置加载成功')
  } catch (error) {
    console.error('获取任务配置失败:', error)
    ElMessage.error('获取任务配置失败')
  } finally {
    loading.value = false
  }
}

// 显示标签输入框
const showTagInput = () => {
  inputTagVisible.value = true
  nextTick(() => {
    tagInput.value.focus()
  })
}

// 添加标签
const addTag = () => {
  if (inputTagValue.value) {
    if (!config.tags.includes(inputTagValue.value)) {
      config.tags.push(inputTagValue.value)
    }
    inputTagValue.value = ''
  }
  inputTagVisible.value = false
}

// 移除标签
const removeTag = (tag: string) => {
  const index = config.tags.indexOf(tag)
  if (index > -1) {
    config.tags.splice(index, 1)
  }
}

// 处理音乐选择
const handleMusicSelected = (selectedMusic: any[]) => {
  config.selectedMusic = selectedMusic
  console.log('选中的音乐:', selectedMusic)
}

// 保存配置
const saveConfig = async () => {
  try {
    loading.value = true

    console.log('准备保存任务配置:', config)

    // 将Proxy对象转换为普通对象，确保序列化正确
    const configToSend = JSON.parse(JSON.stringify(config))
    console.log('转换后的配置对象:', configToSend)

    // 直接触发事件，让父组件处理API调用
    // 这样可以确保配置数据正确传递
    emit('config-completed', configToSend)

    ElMessage.success('配置保存成功')
  } catch (error) {
    console.error('保存任务配置失败:', error)
    ElMessage.error('保存任务配置失败')
  } finally {
    loading.value = false
  }
}

// 保存配置并继续下一步
const saveConfigAndNext = async () => {
  try {
    loading.value = true

    console.log('=== 保存配置并继续 ===')
    console.log('当前完整配置:', config)
    console.log('特别检查contentType:', config.contentType)
    console.log('特别检查selectedMusic:', config.selectedMusic)
    console.log('selectedMusic类型:', typeof config.selectedMusic)
    console.log('selectedMusic长度:', config.selectedMusic ? config.selectedMusic.length : 'undefined')

    if (config.selectedMusic && config.selectedMusic.length > 0) {
      console.log('✅ 检测到选中的音乐:')
      config.selectedMusic.forEach((music, index) => {
        console.log(`  音乐${index + 1}:`, music)
      })
    } else {
      console.log('❌ 未检测到选中的音乐')
    }

    // 将Proxy对象转换为普通对象，确保序列化正确
    const configToSend = JSON.parse(JSON.stringify(config))
    console.log('转换后的配置对象:', configToSend)
    console.log('转换后的selectedMusic:', configToSend.selectedMusic)

    // 触发配置完成事件，父组件会处理保存并自动跳转到下一步
    emit('config-completed', configToSend)

    ElMessage.success('配置保存成功，即将进入下一步')
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.task-config {
  padding: 20px;
}

.tag-item {
  margin-right: 8px;
  margin-bottom: 8px;
}

.tag-input {
  width: 100px;
  margin-right: 8px;
  vertical-align: bottom;
}

.schedule-time {
  margin-top: 10px;
}
</style>
