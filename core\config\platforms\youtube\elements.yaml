# YouTube平台元素定位配置
# 每个元素支持多种定位方法，按优先级排序

elements:
  # 创建按钮（+按钮）
  create_button:
    - type: "accessibility_id"
      value: "创作"
      timeout: 10
      description: "通过无障碍ID定位创作按钮"
    - type: "id"
      value: "com.google.android.youtube:id/image"
      timeout: 5
      description: "通过resource-id定位创建按钮"
    - type: "id"
      value: "com.google.android.youtube:id/fab"
      timeout: 5
      description: "通过fab resource-id定位"
    - type: "coordinate"
      value: [450, 1552]
      description: "通过坐标点击创建按钮"

  # 短视频选项
  shorts_option:
    - type: "xpath"
      value: "//android.widget.TextView[@text='短视频']"
      timeout: 10
      description: "通过文本定位短视频选项"
    - type: "coordinate_relative"
      value: [0.375, 0.75]  # 相对屏幕尺寸的坐标
      description: "通过相对坐标点击短视频选项（第二个位置）"

  # 普通视频上传选项
  video_upload_option:
    - type: "xpath"
      value: "//android.widget.TextView[@text='上传视频']"
      timeout: 10
      description: "通过文本定位上传视频选项"

  # 草稿对话框 - 重新开始按钮
  draft_restart_button:
    - type: "xpath"
      value: "//android.widget.Button[@text='重新开始']"
      timeout: 5
      description: "通过文本定位重新开始按钮"
    - type: "id"
      value: "android:id/button2"
      timeout: 5
      description: "通过resource-id定位重新开始按钮"
    - type: "coordinate"
      value: [613, 913]
      description: "通过坐标点击重新开始按钮（根据bounds [533,871][693,955]的中心点）"

  # 草稿对话框 - 删除草稿按钮
  draft_delete_button:
    - type: "xpath"
      value: "//android.widget.Button[@text='删除草稿']"
      timeout: 5
      description: "通过文本定位删除草稿按钮"
    - type: "xpath"
      value: "//android.widget.Button[@text='删除']"
      timeout: 5
      description: "通过文本定位删除按钮"
    - type: "id"
      value: "android:id/button1"
      timeout: 5
      description: "通过resource-id定位删除按钮"

  # 导入照片库中的视频按钮
  gallery_button:
    - type: "id"
      value: "com.google.android.youtube:id/reel_camera_gallery_button_delegate"
      timeout: 15
      description: "通过resource-id定位导入照片库按钮（可点击的FrameLayout）"
    - type: "xpath"
      value: "//android.widget.FrameLayout[@content-desc='导入照片库中的视频']"
      timeout: 10
      description: "通过content-desc定位导入照片库按钮"
    - type: "coordinate"
      value: [72, 1392]
      description: "通过坐标点击导入照片库按钮（根据bounds [0,1304][144,1480]的中心点）"

  # 视频文件选择（动态内容）
  video_file:
    - type: "xpath_template"
      value: "//android.widget.ImageView[@content-desc='{filename}']"
      timeout: 10
      description: "通过文件名content-desc定位视频文件"
    - type: "coordinate"
      value: [149, 390]
      description: "通过坐标点击视频文件位置"

  # 完成编辑按钮
  finish_editing_button:
    - type: "id"
      value: "com.google.android.youtube:id/shorts_trim_finish_trim_button"
      timeout: 10
      description: "通过resource-id定位完成按钮"
    - type: "xpath"
      value: "//android.widget.Button[@text='完成']"
      timeout: 5
      description: "通过文本定位完成按钮"
    - type: "xpath"
      value: "//android.widget.Button[@content-desc='将片段添加至项目']"
      timeout: 5
      description: "通过content-desc定位完成按钮"
    - type: "coordinate"
      value: [816, 1528]
      description: "通过坐标点击完成按钮"

  # 添加音效按钮
  add_music_button:
    - type: "id"
      value: "com.google.android.youtube:id/shorts_camera_music_button"
      timeout: 5
      description: "通过resource-id定位添加音效按钮"
    - type: "xpath"
      value: "//android.view.ViewGroup[@resource-id='com.google.android.youtube:id/shorts_camera_music_button']"
      timeout: 5
      description: "通过xpath定位添加音效按钮"

  # 标题输入框
  title_input:
    - type: "id"
      value: "com.google.android.youtube:id/title_edit"
      timeout: 10
      description: "标题输入框"

  # 描述输入框
  description_input:
    - type: "id"
      value: "com.google.android.youtube:id/description_edit"
      timeout: 10
      description: "描述输入框"

  # 隐私设置按钮
  privacy_button:
    - type: "id"
      value: "com.google.android.youtube:id/privacy_spinner"
      timeout: 10
      description: "隐私设置下拉按钮"

  # 隐私选项（动态内容）
  privacy_option:
    - type: "xpath_template"
      value: "//android.widget.TextView[@text='{privacy_text}']"
      timeout: 10
      description: "隐私选项文本"

  # 上传按钮
  upload_button:
    - type: "id"
      value: "com.google.android.youtube:id/upload_button"
      timeout: 10
      description: "上传按钮"
    - type: "coordinate_relative"
      value: [0.9, 0.9]
      description: "通过相对坐标点击上传按钮"

# 等待时间配置
wait_times:
  after_click: 2
  after_app_launch: 5
  after_shorts_selection: 3
  after_video_selection: 2
  after_editing: 8  # 增加编辑完成后的等待时间，等待YouTube处理
  before_upload: 1
  video_processing: 10  # 视频处理专用等待时间

# 重试配置
retry_config:
  max_retries: 3
  retry_delay: 1
  timeout_multiplier: 1.5
