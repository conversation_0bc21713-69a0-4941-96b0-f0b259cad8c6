<template>
  <div class="publish-management">
    <el-steps :active="activeStep" finish-status="success" simple>
      <el-step title="创建任务" />
      <el-step title="配置任务" />
      <el-step title="执行任务" />
      <el-step title="执行结果" />
    </el-steps>

    <div class="step-content">
      <!-- 步骤1: 创建任务 -->
      <div v-if="activeStep === 1">
        <PublishTaskForm @task-created="onTaskCreated" />
      </div>

      <!-- 步骤2: 配置任务 -->
      <div v-if="activeStep === 2">
        <TaskConfigForm :task-id="currentTaskId" @config-completed="onConfigCompleted" />
      </div>

      <!-- 步骤3: 执行任务 -->
      <div v-if="activeStep === 3">
        <TaskExecution :task-id="currentTaskId" @execution-completed="onExecutionCompleted" />
      </div>

      <!-- 步骤4: 执行结果 -->
      <div v-if="activeStep === 4">
        <PublishResultsView :task-id="currentTaskId" @create-new-task="resetFlow" />
      </div>
    </div>

    <div class="step-actions">
      <el-button v-if="activeStep > 1" @click="prevStep">上一步</el-button>
      <!-- 在配置步骤（步骤2）隐藏下一步按钮，强制用户使用组件内的保存按钮 -->
      <el-button
        v-if="activeStep < 4 && activeStep !== 2"
        type="primary"
        @click="nextStep"
        :disabled="!canProceed"
      >
        下一步
      </el-button>

      <!-- 在配置步骤显示提示信息 -->
      <div v-if="activeStep === 2" class="config-step-hint">
        <el-alert
          title="请在上方配置表单中点击'保存配置并继续下一步'按钮"
          type="info"
          :closable="false"
          style="text-align: center;"
        />
      </div>
    </div>

    <!-- 上传状态标签页保留为单独入口 -->
    <div class="upload-status-link">
      <el-link type="primary" @click="showUploadStatus = true">查看上传状态</el-link>
    </div>

    <!-- 上传状态对话框 -->
    <el-dialog
      v-model="showUploadStatus"
      title="上传状态"
      width="80%"
      destroy-on-close
    >
      <UploadStatus />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineAsyncComponent, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { updateTaskConfig } from '@/api/social'

const PublishTaskForm = defineAsyncComponent(() => import('./components/PublishTaskForm.vue'))
const TaskConfigForm = defineAsyncComponent(() => import('./components/TaskConfigForm.vue'))
const TaskExecution = defineAsyncComponent(() => import('./components/TaskExecution.vue'))
const PublishResultsView = defineAsyncComponent(() => import('./components/PublishResultsView.vue'))
const UploadStatus = defineAsyncComponent(() => import('./UploadStatus.vue'))

// 当前步骤
const activeStep = ref(1)

// 当前任务ID
const currentTaskId = ref('')

// 上传状态对话框
const showUploadStatus = ref(false)

// 是否可以进行下一步
const canProceed = computed(() => {
  // 在步骤1，需要有任务ID才能进行下一步
  if (activeStep.value === 1) {
    return !!currentTaskId.value
  }

  // 其他步骤默认可以进行
  return true
})

// 任务创建成功回调
const onTaskCreated = (taskId) => {
  currentTaskId.value = taskId
  ElMessage.success('任务创建成功，请继续配置任务')
  nextStep()
}

// 任务配置完成回调
const onConfigCompleted = async (config: any) => {
  try {
    console.log('=== 任务配置保存开始 ===')
    console.log('保存任务配置:', config)
    console.log('任务ID:', currentTaskId.value)
    console.log('配置数据类型:', typeof config)
    console.log('配置数据键:', Object.keys(config))

    // 特别检查contentType字段
    if (config.contentType) {
      console.log('✅ 检测到contentType字段:', config.contentType)
    } else {
      console.log('❌ 未检测到contentType字段')
    }

    // 调用API保存任务配置
    console.log('正在调用updateTaskConfig API...')
    const result = await updateTaskConfig(currentTaskId.value, config)
    console.log('API调用结果:', result)

    ElMessage.success('任务配置保存成功，请继续执行任务')
    console.log('=== 任务配置保存完成 ===')
    nextStep()
  } catch (error) {
    console.error('=== 任务配置保存失败 ===')
    console.error('保存任务配置失败:', error)
    console.error('错误详情:', error.response?.data || error.message)
    ElMessage.error('保存任务配置失败，请重试')
  }
}

// 任务执行完成回调
const onExecutionCompleted = (result) => {
  ElMessage.success(`任务执行${result.status === 'completed' ? '成功' : '失败'}，请查看执行结果`)
  nextStep()
}

// 下一步
const nextStep = () => {
  if (activeStep.value < 4) {
    activeStep.value++
  }
}

// 上一步
const prevStep = () => {
  if (activeStep.value > 1) {
    activeStep.value--
  }
}

// 重置流程
const resetFlow = () => {
  activeStep.value = 1
  currentTaskId.value = ''
  ElMessage.info('开始创建新任务')
}
</script>

<style scoped>
.publish-management {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.step-content {
  margin: 30px 0;
  min-height: 400px;
}

.step-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.upload-status-link {
  text-align: right;
  margin-top: 20px;
}
</style>