"""
任务执行器服务
负责执行发布任务，与雷电模拟器交互
"""

import os
import logging
import asyncio
import datetime
import json
import time
import random
from typing import Dict, List, Any, Optional

# 尝试导入redis.asyncio，如果失败则使用标准redis
try:
    import redis.asyncio as redis
    logger = logging.getLogger(__name__)
    logger.info("成功导入redis.asyncio模块")
except ImportError:
    import redis
    logger = logging.getLogger(__name__)
    logger.warning("redis.asyncio模块导入失败，使用标准redis模块")

    # 创建一个兼容层，使标准redis看起来像异步redis
    class AsyncRedisWrapper:
        def __init__(self, redis_client):
            self.redis_client = redis_client

        async def ping(self):
            return self.redis_client.ping()

        async def publish(self, channel, message):
            return self.redis_client.publish(channel, message)

        async def set(self, key, value):
            return self.redis_client.set(key, value)

        async def get(self, key):
            return self.redis_client.get(key)

        async def expire(self, key, time):
            return self.redis_client.expire(key, time)

    # 修改redis.from_url函数以返回包装的客户端
    original_from_url = redis.from_url
    def async_from_url(*args, **kwargs):
        client = original_from_url(*args, **kwargs)
        return AsyncRedisWrapper(client)
    redis.from_url = async_from_url

from src.devices.base import DeviceStatus

# 避免循环导入
# from src.main_service import CoreMainService

# 导入YouTube任务执行器
from .youtube.youtube_task_executor import YouTubeTaskExecutor

logger = logging.getLogger(__name__)

class TaskExecutor:
    """任务执行器类"""

    def __init__(self, main_service: 'CoreMainService'):
        """初始化任务执行器

        Args:
            main_service: Core主服务实例
        """
        self.main_service = main_service
        self.tasks = {}  # 任务缓存
        self.running_tasks = {}  # 正在运行的任务
        self.task_logs = {}  # 任务日志
        self.redis_client = None
        self.is_initialized = False

        # 初始化YouTube任务执行器
        self.youtube_task_executor = YouTubeTaskExecutor(main_service)

        # 任务执行状态
        self.task_status = {
            "pending": "等待执行",
            "running": "正在执行",
            "paused": "已暂停",
            "completed": "已完成",
            "failed": "执行失败",
            "canceled": "已取消"
        }

        logger.info("任务执行器初始化")

    async def initialize(self) -> bool:
        """初始化任务执行器"""
        try:
            # 尝试连接Redis
            redis_connected = await self._reconnect_redis()

            if not redis_connected:
                logger.warning("Redis连接失败，将使用内存存储任务状态")
                self.redis_client = None

            self.is_initialized = True
            logger.info("任务执行器初始化完成")
            return True

        except Exception as e:
            logger.error(f"初始化任务执行器异常: {str(e)}", exc_info=True)
            return False

    async def create_task(self, task_data: Dict[str, Any]) -> bool:
        """创建任务

        Args:
            task_data: 任务数据

        Returns:
            bool: 是否成功
        """
        try:
            task_id = task_data.get("task_id")
            if not task_id:
                logger.error("任务数据缺少task_id字段")
                return False

            # 保存任务数据
            self.tasks[task_id] = task_data

            # 初始化任务日志
            self.task_logs[task_id] = []
            self.add_task_log(task_id, "任务已创建", "info")

            # 发布任务状态到Redis
            await self.publish_task_status(task_id)

            logger.info(f"任务{task_id}创建成功")
            return True

        except Exception as e:
            logger.error(f"创建任务异常: {str(e)}", exc_info=True)
            return False

    async def start_task(self, task_id: str) -> bool:
        """开始执行任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功
        """
        try:
            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.error(f"任务{task_id}不存在")
                return False

            # 获取任务数据
            task = self.tasks[task_id]

            # 检查任务状态
            if task.get("status") == "running":
                logger.warning(f"任务{task_id}已经在运行")
                return True

            # 检查设备是否可用
            device_id = task.get("device_id")
            if not device_id:
                logger.error(f"任务{task_id}未指定设备ID")
                return False

            # 获取设备状态
            device_controller = self.main_service.ldplayer_manager.devices.get(device_id)
            if not device_controller:
                logger.error(f"设备{device_id}不存在")
                self.add_task_log(task_id, f"设备{device_id}不存在", "error")
                return False

            device_status = await device_controller.get_status()
            if device_status != DeviceStatus.RUNNING:
                # 尝试启动设备
                logger.info(f"设备{device_id}未运行，尝试启动")
                self.add_task_log(task_id, f"设备{device_id}未运行，尝试启动", "info")

                start_success = await device_controller.start()
                if not start_success:
                    logger.error(f"启动设备{device_id}失败")
                    self.add_task_log(task_id, f"启动设备{device_id}失败", "error")
                    return False

                # 等待设备启动完成
                for _ in range(30):  # 最多等待30秒
                    await asyncio.sleep(1)
                    status = await device_controller.get_status()
                    if status == DeviceStatus.RUNNING:
                        break
                else:
                    logger.error(f"设备{device_id}启动超时")
                    self.add_task_log(task_id, f"设备{device_id}启动超时", "error")
                    return False

            # 更新任务状态
            task["status"] = "running"
            task["start_time"] = datetime.datetime.now().isoformat()

            # 计算预计完成时间（30分钟后）
            end_time = datetime.datetime.now() + datetime.timedelta(minutes=30)
            task["estimated_end_time"] = end_time.isoformat()

            # 初始化进度
            task["progress"] = 0

            # 添加日志
            self.add_task_log(task_id, "任务开始执行", "success")

            # 发布任务状态到Redis
            await self.publish_task_status(task_id)

            # 再次发布任务状态到Redis，确保状态更新被接收
            await asyncio.sleep(0.5)
            await self.publish_task_status(task_id)

            # 启动任务执行协程
            asyncio.create_task(self.execute_task(task_id))

            # 记录详细日志
            logger.info(f"任务{task_id}状态已发布到Redis，channel: task:{task_id}:status")

            logger.info(f"任务{task_id}开始执行")
            return True

        except Exception as e:
            logger.error(f"开始执行任务异常: {str(e)}", exc_info=True)
            self.add_task_log(task_id, f"开始执行任务异常: {str(e)}", "error")
            return False

    async def execute_task(self, task_id: str) -> None:
        """执行任务

        Args:
            task_id: 任务ID
        """
        try:
            # 记录正在运行的任务
            self.running_tasks[task_id] = True

            # 获取任务数据
            task = self.tasks[task_id]

            # 获取设备ID
            device_id = task.get("device_id")

            # 获取内容路径
            content_path = task.get("content_path")

            # 获取平台ID
            platform_id = task.get("platform_id")

            # 获取账号ID
            account_id = task.get("account_id")

            # 添加日志
            self.add_task_log(task_id, f"准备在设备{device_id}上执行任务", "info")
            self.add_task_log(task_id, f"内容路径: {content_path}", "info")
            self.add_task_log(task_id, f"平台ID: {platform_id}", "info")
            self.add_task_log(task_id, f"账号ID: {account_id}", "info")

            # 根据平台ID选择不同的任务执行器
            # 记录原始平台ID
            logger.info(f"原始平台ID: {platform_id}, 类型: {type(platform_id)}")

            # 检查是否是YouTube任务
            is_youtube = False

            # 方法1：检查平台ID
            platform_id_str = str(platform_id).lower()
            if platform_id_str == "youtube" or platform_id_str == "youtube2" or "youtube" in platform_id_str:
                is_youtube = True
                logger.info(f"通过平台ID检测到YouTube任务: {platform_id}")

                # 记录平台ID类型，便于调试
                if platform_id_str == "youtube":
                    logger.info("平台ID是标准YouTube ID")
                elif platform_id_str == "youtube2":
                    logger.info("平台ID是YouTube2 ID")
                elif "youtube" in platform_id_str:
                    logger.info("平台ID包含YouTube字符串")

            # 方法2：检查任务数据中是否有YouTube特有的字段
            if "metadata" in task and isinstance(task["metadata"], dict):
                metadata = task["metadata"]
                if "titleTemplate" in metadata or "privacyStatus" in metadata:
                    is_youtube = True
                    logger.info("通过元数据字段检测到YouTube任务")

            # 方法3：检查任务是否有folder_path而不是content_path
            if "folder_path" in task and not task.get("content_path"):
                is_youtube = True
                # 将folder_path设置为content_path
                task["content_path"] = task.get("folder_path")
                logger.info(f"通过folder_path字段检测到YouTube任务: {task.get('folder_path')}")

            # 方法4：检查内容路径是否包含"youtube"
            if content_path and "youtube" in content_path.lower():
                is_youtube = True
                logger.info(f"通过内容路径检测到YouTube任务: {content_path}")

            # 方法5：检查账号ID
            if account_id:
                # 检查账号ID是否与YouTube相关
                account_id_str = str(account_id).lower()
                if "youtube" in account_id_str or "yt" in account_id_str:
                    is_youtube = True
                    logger.info(f"通过账号ID检测到YouTube任务: {account_id}")

            # 方法6：检查任务ID
            if task_id:
                # 检查任务ID是否与YouTube相关
                task_id_str = str(task_id).lower()
                if "youtube" in task_id_str or "yt" in task_id_str:
                    is_youtube = True
                    logger.info(f"通过任务ID检测到YouTube任务: {task_id}")

            if is_youtube:
                # 使用YouTube任务执行器
                self.add_task_log(task_id, f"使用YouTube任务执行器 (平台ID: {platform_id})", "info")
                logger.info(f"任务{task_id}使用YouTube任务执行器，平台ID: {platform_id}")

                # 不覆盖平台ID，使用原始平台ID
                # task["platform_id"] = "youtube"

                success = await self.youtube_task_executor.execute_youtube_upload_task(
                    task_id,
                    task,
                    self.add_task_log,
                    self.publish_task_status
                )

                # 根据执行结果更新任务状态
                if success and task.get("status") != "canceled":
                    task["status"] = "completed"
                    task["progress"] = 100
                    self.add_task_log(task_id, "YouTube上传任务执行完成", "success")
                elif task.get("status") != "canceled" and task.get("status") != "completed":
                    task["status"] = "failed"
                    self.add_task_log(task_id, "YouTube上传任务执行失败", "error")

                # 发布最终状态到Redis
                await self.publish_task_status(task_id)
                return

            # 默认任务执行逻辑（模拟）
            self.add_task_log(task_id, f"使用默认任务执行器，平台: {platform_id}", "info")

            # 模拟任务执行过程
            total_steps = 20
            for step in range(1, total_steps + 1):
                # 检查任务是否被取消或暂停
                if task.get("status") == "canceled":
                    logger.info(f"任务{task_id}已取消")
                    break

                if task.get("status") == "paused":
                    logger.info(f"任务{task_id}已暂停")
                    # 等待恢复
                    while task.get("status") == "paused":
                        await asyncio.sleep(1)

                    # 如果恢复后状态为canceled，则退出
                    if task.get("status") == "canceled":
                        logger.info(f"任务{task_id}已取消")
                        break

                # 更新进度
                progress = int(step / total_steps * 100)
                task["progress"] = progress

                # 随机添加日志
                if random.random() > 0.7:
                    messages = [
                        "正在处理文件...",
                        "上传内容中...",
                        "验证内容...",
                        "应用元数据...",
                        "等待服务器响应..."
                    ]
                    random_message = messages[random.randint(0, len(messages) - 1)]
                    self.add_task_log(task_id, random_message, "info")

                # 随机更新设备使用情况
                task["device_usage"] = {
                    "cpu": random.randint(40, 70),
                    "memory": random.randint(60, 80),
                    "network": "已连接"
                }

                # 发布任务状态到Redis
                await self.publish_task_status(task_id)

                # 模拟执行时间
                await asyncio.sleep(1.5)

            # 任务完成
            if task.get("status") != "canceled":
                task["status"] = "completed"
                task["progress"] = 100
                self.add_task_log(task_id, "任务执行完成", "success")

            # 发布最终状态到Redis
            await self.publish_task_status(task_id)

        except Exception as e:
            logger.error(f"执行任务{task_id}异常: {str(e)}", exc_info=True)
            self.add_task_log(task_id, f"执行任务异常: {str(e)}", "error")

            # 更新任务状态为失败
            if task_id in self.tasks:
                self.tasks[task_id]["status"] = "failed"
                await self.publish_task_status(task_id)

        finally:
            # 移除正在运行的任务
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    async def pause_task(self, task_id: str) -> bool:
        """暂停任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功
        """
        try:
            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.error(f"任务{task_id}不存在")
                return False

            # 获取任务数据
            task = self.tasks[task_id]

            # 检查任务状态
            if task.get("status") != "running":
                logger.warning(f"任务{task_id}不在运行状态，无法暂停")
                return False

            # 更新任务状态
            task["status"] = "paused"

            # 添加日志
            self.add_task_log(task_id, "任务已暂停", "warning")

            # 发布任务状态到Redis
            await self.publish_task_status(task_id)

            logger.info(f"任务{task_id}已暂停")
            return True

        except Exception as e:
            logger.error(f"暂停任务异常: {str(e)}", exc_info=True)
            return False

    async def cancel_task(self, task_id: str) -> bool:
        """取消任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功
        """
        try:
            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.error(f"任务{task_id}不存在")
                return False

            # 获取任务数据
            task = self.tasks[task_id]

            # 检查任务状态
            if task.get("status") not in ["running", "paused", "pending"]:
                logger.warning(f"任务{task_id}状态为{task.get('status')}，无法取消")
                return False

            # 更新任务状态
            task["status"] = "canceled"

            # 添加日志
            self.add_task_log(task_id, "任务已取消", "warning")

            # 发布任务状态到Redis
            await self.publish_task_status(task_id)

            logger.info(f"任务{task_id}已取消")
            return True

        except Exception as e:
            logger.error(f"取消任务异常: {str(e)}", exc_info=True)
            return False

    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            Optional[Dict[str, Any]]: 任务状态
        """
        try:
            # 检查任务是否存在
            if task_id not in self.tasks:
                logger.error(f"任务{task_id}不存在")
                return None

            # 获取任务数据
            task = self.tasks[task_id]

            # 构建状态数据
            status_data = {
                "task_id": task_id,
                "status": task.get("status", "unknown"),
                "progress": task.get("progress", 0),
                "start_time": task.get("start_time", ""),
                "estimated_end_time": task.get("estimated_end_time", ""),
                "device_usage": task.get("device_usage", {
                    "cpu": 0,
                    "memory": 0,
                    "network": "未知"
                }),
                "logs": self.get_recent_logs(task_id, 10),  # 获取最近10条日志
                "details": {
                    "platform_id": task.get("platform_id", ""),
                    "account_id": task.get("account_id", ""),
                    "device_id": task.get("device_id", ""),
                    "content_path": task.get("content_path", ""),
                    "created_at": task.get("created_at", "")
                },
                "message": self.get_status_message(task),
                "current_step": self.get_current_step(task),
                "steps": self.get_task_steps(task)
            }

            return status_data

        except Exception as e:
            logger.error(f"获取任务状态异常: {str(e)}", exc_info=True)
            return None

    async def get_task_logs(self, task_id: str) -> List[Dict[str, Any]]:
        """获取任务日志

        Args:
            task_id: 任务ID

        Returns:
            List[Dict[str, Any]]: 任务日志
        """
        try:
            # 检查任务是否存在
            if task_id not in self.task_logs:
                logger.error(f"任务{task_id}日志不存在")
                return []

            # 获取任务日志
            logs = self.task_logs[task_id]

            return logs

        except Exception as e:
            logger.error(f"获取任务日志异常: {str(e)}", exc_info=True)
            return []

    def add_task_log(self, task_id: str, message: str, level: str = "info") -> None:
        """添加任务日志

        Args:
            task_id: 任务ID
            message: 日志消息
            level: 日志级别
        """
        try:
            # 检查任务日志是否存在
            if task_id not in self.task_logs:
                self.task_logs[task_id] = []

            # 添加日志
            log_entry = {
                "message": message,
                "level": level,
                "timestamp": datetime.datetime.now().isoformat()
            }

            self.task_logs[task_id].append(log_entry)

            # 日志最多保留1000条
            if len(self.task_logs[task_id]) > 1000:
                self.task_logs[task_id] = self.task_logs[task_id][-1000:]

            # 记录到系统日志
            log_func = getattr(logger, level, logger.info)
            log_func(f"任务{task_id}: {message}")

        except Exception as e:
            logger.error(f"添加任务日志异常: {str(e)}", exc_info=True)

    def get_recent_logs(self, task_id: str, count: int = 10) -> List[Dict[str, Any]]:
        """获取最近的日志

        Args:
            task_id: 任务ID
            count: 日志数量

        Returns:
            List[Dict[str, Any]]: 最近的日志
        """
        try:
            # 检查任务日志是否存在
            if task_id not in self.task_logs:
                return []

            # 获取最近的日志
            logs = self.task_logs[task_id]

            return logs[-count:] if len(logs) > count else logs

        except Exception as e:
            logger.error(f"获取最近日志异常: {str(e)}", exc_info=True)
            return []

    def get_status_message(self, task: Dict[str, Any]) -> str:
        """获取任务状态消息

        Args:
            task: 任务数据

        Returns:
            str: 状态消息
        """
        status = task.get("status", "unknown")

        # 根据状态返回消息
        if status == "pending":
            return "任务已创建，等待执行"
        elif status == "running":
            progress = task.get("progress", 0)
            if progress < 20:
                return "正在连接设备..."
            elif progress < 40:
                return "正在启动应用..."
            elif progress < 60:
                return "正在处理内容..."
            elif progress < 80:
                return "正在上传内容..."
            else:
                return "正在完成任务..."
        elif status == "paused":
            return "任务已暂停，等待恢复"
        elif status == "completed":
            return "任务已成功完成"
        elif status == "failed":
            return "任务执行失败"
        elif status == "canceled":
            return "任务已取消"
        else:
            return "未知状态"

    def get_current_step(self, task: Dict[str, Any]) -> int:
        """获取当前步骤

        Args:
            task: 任务数据

        Returns:
            int: 当前步骤
        """
        status = task.get("status", "unknown")
        progress = task.get("progress", 0)

        # 根据状态和进度返回当前步骤
        if status == "pending":
            return 1
        elif status == "running":
            if progress < 20:
                return 2  # 连接设备
            elif progress < 40:
                return 3  # 启动应用
            elif progress < 80:
                return 4  # 执行操作
            else:
                return 5  # 完成任务
        elif status == "completed":
            return 5
        elif status == "failed" or status == "canceled":
            # 如果失败或取消，返回最后一个进行中的步骤
            if progress < 20:
                return 2
            elif progress < 40:
                return 3
            elif progress < 80:
                return 4
            else:
                return 5
        else:
            return 1

    def get_task_steps(self, task: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取任务步骤

        Args:
            task: 任务数据

        Returns:
            List[Dict[str, Any]]: 任务步骤
        """
        status = task.get("status", "unknown")
        progress = task.get("progress", 0)
        current_step = self.get_current_step(task)

        # 初始化步骤
        steps = [
            {"step": 1, "name": "准备任务", "status": "success", "message": "任务准备就绪"},
            {"step": 2, "name": "连接设备", "status": "wait", "message": "等待任务开始"},
            {"step": 3, "name": "启动应用", "status": "wait", "message": "等待设备连接完成"},
            {"step": 4, "name": "执行操作", "status": "wait", "message": "等待应用启动完成"},
            {"step": 5, "name": "完成任务", "status": "wait", "message": "等待操作执行完成"}
        ]

        # 更新步骤状态
        for i in range(5):
            step_num = i + 1

            if step_num < current_step:
                # 之前的步骤已完成
                steps[i]["status"] = "success"

                # 更新消息
                if step_num == 1:
                    steps[i]["message"] = "任务准备就绪"
                elif step_num == 2:
                    steps[i]["message"] = "设备连接成功"
                elif step_num == 3:
                    steps[i]["message"] = "应用启动成功"
                elif step_num == 4:
                    steps[i]["message"] = "操作执行成功"

            elif step_num == current_step:
                # 当前步骤
                if status == "running":
                    steps[i]["status"] = "process"

                    # 更新消息
                    if step_num == 2:
                        steps[i]["message"] = "正在连接设备..."
                    elif step_num == 3:
                        steps[i]["message"] = "正在启动应用..."
                    elif step_num == 4:
                        if progress < 60:
                            steps[i]["message"] = "正在处理内容..."
                        else:
                            steps[i]["message"] = "正在上传内容..."
                    elif step_num == 5:
                        steps[i]["message"] = "正在完成任务..."

                elif status == "paused":
                    steps[i]["status"] = "warning"
                    steps[i]["message"] = "已暂停"
                elif status == "failed":
                    steps[i]["status"] = "error"
                    steps[i]["message"] = "执行失败"
                elif status == "canceled":
                    steps[i]["status"] = "warning"
                    steps[i]["message"] = "已取消"
                elif status == "completed" and step_num == 5:
                    steps[i]["status"] = "success"
                    steps[i]["message"] = "任务已完成"

        return steps

    async def publish_task_status(self, task_id: str) -> None:
        """发布任务状态到Redis

        Args:
            task_id: 任务ID
        """
        try:
            # 检查Redis客户端是否可用
            if not self.redis_client:
                # 尝试重新连接Redis
                await self._reconnect_redis()

                # 如果重连后仍然不可用，则使用内存存储
                if not self.redis_client:
                    logger.warning(f"Redis客户端不可用，无法发布任务{task_id}状态")
                    # 保存到内存中
                    self._save_task_status_to_memory(task_id)
                    return

            # 获取任务状态
            status = await self.get_task_status(task_id)
            if not status:
                logger.warning(f"获取任务{task_id}状态失败，无法发布状态")
                return

            # 发布到Redis
            channel = f"task:{task_id}:status"
            status_json = json.dumps(status)

            # 记录详细日志
            logger.debug(f"发布任务{task_id}状态到Redis，channel: {channel}")
            logger.debug(f"任务{task_id}状态数据: {status_json[:200]}...")

            try:
                # 发布状态
                publish_result = await self.redis_client.publish(channel, status_json)
                logger.debug(f"Redis发布结果: {publish_result}个客户端接收到消息")

                # 同时保存最新状态
                key = f"task:{task_id}:latest"
                await self.redis_client.set(key, status_json)

                # 设置过期时间（24小时）
                await self.redis_client.expire(key, 86400)

                # 确认状态已保存
                saved_status = await self.redis_client.get(key)
                if saved_status:
                    logger.debug(f"任务{task_id}状态已保存到Redis，key: {key}")
                else:
                    logger.warning(f"任务{task_id}状态保存失败")
            except Exception as redis_error:
                logger.error(f"Redis操作失败: {str(redis_error)}", exc_info=True)
                # 尝试重新连接
                await self._reconnect_redis()
                # 保存到内存中
                self._save_task_status_to_memory(task_id)

        except Exception as e:
            logger.error(f"发布任务{task_id}状态异常: {str(e)}", exc_info=True)
            # 尝试重新连接Redis
            await self._reconnect_redis()

    async def _reconnect_redis(self) -> bool:
        """尝试重新连接Redis

        Returns:
            bool: 是否成功重连
        """
        try:
            # 首先尝试从main_service.settings获取
            redis_url = None
            if hasattr(self.main_service, 'settings') and self.main_service.settings and hasattr(self.main_service.settings, 'redis_url'):
                redis_url = self.main_service.settings.redis_url
                logger.info(f"从settings获取Redis URL: {redis_url}")

            # 如果无法从settings获取，尝试从环境变量获取
            if not redis_url:
                import os
                redis_url = os.environ.get("REDIS_URL")
                if redis_url:
                    logger.info(f"从环境变量获取Redis URL: {redis_url}")

            # 如果仍然无法获取，使用硬编码的默认值
            if not redis_url:
                # 使用与设备同步服务相同的默认值
                redis_url = "redis://192.168.123.137:6379/1"
                logger.info(f"使用默认Redis URL: {redis_url}")

            # 使用与设备同步服务相同的连接方式
            try:
                # 创建Redis客户端
                self.redis_client = redis.from_url(redis_url)

                # 测试连接
                await self.redis_client.ping()
                logger.info(f"Redis连接成功: {redis_url}")
                return True
            except Exception as redis_error:
                logger.error(f"Redis连接失败: {str(redis_error)}", exc_info=True)
                self.redis_client = None
                return False
        except Exception as reconnect_error:
            logger.error(f"重新连接Redis失败: {str(reconnect_error)}", exc_info=True)
            self.redis_client = None
            return False

    def _save_task_status_to_memory(self, task_id: str) -> None:
        """将任务状态保存到内存中

        Args:
            task_id: 任务ID
        """
        # 这里可以实现内存存储逻辑，例如保存到文件或内存缓存
        logger.info(f"任务{task_id}状态已保存到内存中")
        # 可以在这里实现备用存储机制，如保存到本地文件

    async def shutdown(self) -> None:
        """关闭任务执行器"""
        try:
            logger.info("正在关闭任务执行器...")

            # 取消所有正在运行的任务
            running_tasks = list(self.running_tasks.keys())
            for task_id in running_tasks:
                try:
                    logger.info(f"正在取消任务: {task_id}")
                    await self.cancel_task(task_id)
                except Exception as task_error:
                    logger.error(f"取消任务 {task_id} 失败: {str(task_error)}")

            # 关闭Redis连接
            if self.redis_client:
                try:
                    await self.redis_client.close()
                    logger.info("Redis连接已关闭")
                except Exception as redis_error:
                    logger.error(f"关闭Redis连接失败: {str(redis_error)}")

            self.redis_client = None
            self.is_initialized = False
            logger.info("任务执行器已关闭")

        except Exception as e:
            logger.error(f"关闭任务执行器异常: {str(e)}", exc_info=True)
