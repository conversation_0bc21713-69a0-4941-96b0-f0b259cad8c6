"""
基础上传器
为各种社交媒体应用提供通用的上传功能基类
"""

import logging
import asyncio
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional

from .device_manager import DeviceManager
from .network_manager import NetworkManager
from .v2ray_manager import V2rayManager
from .file_manager import FileManager

logger = logging.getLogger(__name__)


class BaseUploader(ABC):
    """基础上传器抽象类"""

    def __init__(self, device_id: str, app_package: str, app_activity: str, appium_server: str = 'http://localhost:4723'):
        """初始化基础上传器

        Args:
            device_id: 设备ID
            app_package: 应用包名
            app_activity: 应用活动名
            appium_server: Appium服务器地址
        """
        # 初始化各个管理器
        self.device_manager = DeviceManager(device_id, appium_server)
        self.network_manager = NetworkManager(device_id)
        # 设置网络管理器的设备管理器引用，以便获取正确的设备ID
        self.network_manager.set_device_manager(self.device_manager)
        self.v2ray_manager = V2rayManager(self.device_manager)
        self.file_manager = FileManager(self.device_manager)

        # 应用信息
        self.app_package = app_package
        self.app_activity = app_activity

        # 上传状态
        self.upload_progress = 0
        self.upload_status = "pending"
        self.upload_message = ""

    async def connect(self) -> bool:
        """连接到设备

        Returns:
            bool: 是否连接成功
        """
        try:
            logger.info("===== 开始连接设备 =====")

            # 连接设备
            success = await self.device_manager.connect()
            if not success:
                self.upload_status = "error"
                self.upload_message = "连接设备失败"
                return False

            # 检查网络连接
            network_status = await self.network_manager.check_network_connection()
            logger.info(f"网络状态: {network_status}")

            # 网络检查失败时不直接退出，而是记录状态，后续在ensure_network_ready中尝试启动V2rayN
            if network_status["status"] == "error":
                logger.warning("初始网络检查失败，将在后续尝试启动V2rayN")
                self.upload_message = "网络连接异常，将尝试启动代理"
            elif network_status["status"] == "warning":
                self.upload_message = network_status["message"]
            else:
                self.upload_message = "网络连接正常"

            logger.info("设备连接成功")
            return True

        except Exception as e:
            logger.error(f"连接设备异常: {str(e)}", exc_info=True)
            self.upload_status = "error"
            self.upload_message = f"连接设备异常: {str(e)}"
            return False

    async def disconnect(self) -> None:
        """断开连接"""
        await self.device_manager.disconnect()

    async def get_upload_status(self) -> Dict[str, Any]:
        """获取上传状态

        Returns:
            Dict[str, Any]: 上传状态信息
        """
        return {
            "status": self.upload_status,
            "progress": self.upload_progress,
            "message": self.upload_message
        }

    async def ensure_network_ready(self) -> None:
        """确保网络连接就绪"""
        try:
            logger.info("确保网络连接就绪...")

            # 首先检查当前网络状态
            network_status = await self.network_manager.check_network_connection()
            logger.info(f"当前网络状态: {network_status}")

            # 如果网络连接异常，尝试启动V2rayN
            if network_status["status"] == "error":
                logger.info("网络连接异常，尝试启动V2rayN代理...")
                self.upload_message = "网络连接异常，正在启动V2rayN代理..."

                # 尝试启动V2rayN
                v2ray_success = await self.v2ray_manager.launch_and_connect()
                if v2ray_success:
                    logger.info("V2rayN启动成功，重新检查网络连接...")
                    # 等待代理生效
                    await asyncio.sleep(3)

                    # 重新检查网络连接
                    network_status = await self.network_manager.check_network_connection()
                    logger.info(f"启动V2rayN后的网络状态: {network_status}")

                    if network_status["status"] != "error":
                        logger.info("启动V2rayN后网络连接恢复正常")
                        self.upload_message = "V2rayN启动成功，网络连接正常"
                    else:
                        logger.warning("启动V2rayN后网络连接仍然异常，但继续执行")
                        self.upload_message = "V2rayN已启动，但网络连接仍受限，继续执行"
                else:
                    logger.warning("V2rayN启动失败，但继续执行")
                    self.upload_message = "V2rayN启动失败，网络连接受限，但继续执行"
            else:
                # 网络连接正常或有警告，尝试确保Google访问
                google_ready = await self.network_manager.ensure_google_access(self.v2ray_manager)

                if google_ready:
                    logger.info("网络连接就绪")
                    self.upload_message = "网络连接正常"
                else:
                    logger.warning("无法确保Google访问，但继续执行")
                    self.upload_message = "网络连接受限，但继续执行"

        except Exception as e:
            logger.warning(f"网络准备异常: {str(e)}")
            self.upload_message = "网络状态未知，但继续执行"

    async def launch_app(self) -> bool:
        """启动应用

        Returns:
            bool: 是否成功启动
        """
        try:
            logger.info(f"启动应用: {self.app_package}")
            self.upload_message = f"正在启动{self.get_app_name()}应用..."

            success = await self.device_manager.launch_app(self.app_package, self.app_activity)
            if not success:
                logger.error(f"启动{self.get_app_name()}应用失败")
                self.upload_status = "error"
                self.upload_message = f"启动{self.get_app_name()}应用失败"
                return False

            # 验证应用是否成功启动
            if not await self.verify_app_launched():
                logger.error(f"{self.get_app_name()}应用未成功启动")
                self.upload_status = "error"
                self.upload_message = f"{self.get_app_name()}应用未成功启动"
                return False

            logger.info(f"{self.get_app_name()}应用已成功启动")
            self.upload_message = f"{self.get_app_name()}应用已启动"
            return True

        except Exception as e:
            logger.error(f"启动{self.get_app_name()}应用异常: {str(e)}", exc_info=True)
            self.upload_status = "error"
            self.upload_message = f"启动{self.get_app_name()}应用异常: {str(e)}"
            return False

    async def prepare_media_file(self, file_path: str) -> bool:
        """准备媒体文件

        Args:
            file_path: 文件路径

        Returns:
            bool: 是否准备成功
        """
        try:
            logger.info("准备媒体文件...")
            self.upload_message = "正在推送媒体文件到设备..."

            # 推送文件到设备
            success = await self.file_manager.push_file_to_device(file_path)
            if not success:
                self.upload_status = "error"
                self.upload_message = "推送媒体文件到设备失败"
                return False

            logger.info("媒体文件已准备就绪")
            self.upload_message = "媒体文件已推送到设备"
            return True

        except Exception as e:
            logger.error(f"准备媒体文件异常: {str(e)}", exc_info=True)
            self.upload_status = "error"
            self.upload_message = f"准备媒体文件异常: {str(e)}"
            return False

    @abstractmethod
    def get_app_name(self) -> str:
        """获取应用名称

        Returns:
            str: 应用名称
        """
        pass

    @abstractmethod
    async def verify_app_launched(self) -> bool:
        """验证应用是否已启动

        Returns:
            bool: 是否已启动
        """
        pass

    @abstractmethod
    async def execute_upload_task(self, *args, **kwargs) -> bool:
        """执行上传任务

        Returns:
            bool: 是否上传成功
        """
        pass

    def update_progress(self, progress: int, message: str) -> None:
        """更新上传进度

        Args:
            progress: 进度百分比
            message: 进度消息
        """
        self.upload_progress = progress
        self.upload_message = message
        logger.info(f"上传进度: {progress}% - {message}")

    def set_status(self, status: str, message: str = "") -> None:
        """设置上传状态

        Args:
            status: 状态
            message: 状态消息
        """
        self.upload_status = status
        if message:
            self.upload_message = message
        logger.info(f"上传状态: {status} - {message}")

    async def input_text_via_adb(self, text: str) -> None:
        """通过ADB输入文本

        Args:
            text: 要输入的文本
        """
        try:
            # 转义特殊字符
            escaped_text = text.replace('"', '\\"').replace("'", "\\'")

            input_process = await asyncio.create_subprocess_shell(
                f"adb -s {self.device_manager.device_id} shell input text \"{escaped_text}\"",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await input_process.communicate()
            logger.info("已通过ADB输入文本")
        except Exception as e:
            logger.warning(f"通过ADB输入文本异常: {str(e)}")
