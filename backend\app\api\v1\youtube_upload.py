import os
import uuid
import logging
import datetime
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from pydantic import BaseModel, Field

from app.core.security import get_current_user
from app.core.schemas.social_repository import SocialDatabaseService
from app.api.social import get_social_service

# 设置日志记录器
logger = logging.getLogger(__name__)

# 定义API路由
router = APIRouter(
    prefix="/api/v1/social/youtube",
    tags=["youtube"],
    dependencies=[Depends(get_current_user)]
)

# 定义数据模型
class VideoMetadata(BaseModel):
    titleTemplate: str = ""
    description: str = ""
    tags: List[str] = []
    privacyStatus: str = "public"
    contentType: str = "video"  # 新增内容类型字段：video, shorts, live, post
    musicLibrary: Optional[str] = None
    publishStrategy: Optional[str] = None

class UploadTaskCreate(BaseModel):
    folderPath: str
    accountId: str
    metadata: VideoMetadata
    selectedFiles: Optional[List[str]] = None

class UploadTaskResponse(BaseModel):
    task_id: str
    status: str
    estimated_time: int = 3600

class UploadTask(BaseModel):
    id: str
    folder_path: str
    account_id: str
    metadata: Dict[str, Any]
    selected_files: Optional[List[str]] = None
    status: str
    created_at: datetime.datetime
    updated_at: Optional[datetime.datetime] = None

class UploadTaskLog(BaseModel):
    task_id: str
    timestamp: datetime.datetime
    message: str
    level: str
    details: Optional[Dict[str, Any]] = None

# 验证文件夹路径
def validate_folder_path(path: str) -> bool:
    """验证文件夹路径是否存在且可访问"""
    try:
        return os.path.exists(path) and os.path.isdir(path)
    except Exception as e:
        logger.error(f"验证文件夹路径失败: {str(e)}")
        return False

# 创建上传任务
@router.post("/uploads", response_model=UploadTaskResponse)
async def create_upload_task(
    task: UploadTaskCreate,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    创建YouTube视频上传任务

    - **folderPath**: 视频文件夹路径
    - **accountId**: YouTube账号ID
    - **metadata**: 视频元数据
    - **selectedFiles**: 可选，选中的文件路径列表
    """
    logger.info(f"创建上传任务: {task.folderPath}, 账号: {task.accountId}")

    # 验证文件夹路径
    if not validate_folder_path(task.folderPath):
        raise HTTPException(status_code=400, detail="无效的文件夹路径")

    # 生成任务ID
    task_id = str(uuid.uuid4())

    # 从数据库获取YouTube平台信息
    # 查询所有平台，找到名称或ID包含"youtube"的平台
    platforms = list(db_service.db.social_platforms.find({}))
    youtube_platform = None

    for platform in platforms:
        platform_id = platform.get("id", "")
        platform_name = platform.get("name", "")
        if "youtube" in str(platform_id).lower() or "youtube" in str(platform_name).lower():
            youtube_platform = platform
            break

    if youtube_platform:
        # 尝试获取平台ID，优先使用platform字段（这是平台的id字段）
        if "platform" in youtube_platform:
            platform_id = youtube_platform.get("platform")
            logger.info(f"使用platform字段作为平台ID: {platform_id}")
        elif "id" in youtube_platform:
            platform_id = youtube_platform.get("id")
            logger.info(f"使用id字段作为平台ID: {platform_id}")
        else:
            # 如果都没有，使用name字段的小写形式
            platform_id = youtube_platform.get("name", "youtube").lower()
            logger.info(f"使用name字段的小写形式作为平台ID: {platform_id}")

        logger.info(f"从数据库获取到YouTube平台ID: {platform_id}, 平台名称: {youtube_platform.get('name')}")
    else:
        # 如果找不到，记录警告并使用任务中指定的平台ID
        logger.warning("在数据库中未找到YouTube平台记录")
        # 查询所有平台，记录调试信息
        logger.info(f"数据库中的平台记录: {[p.get('id') for p in platforms]}")
        platform_id = "youtube2"  # 使用默认值
        logger.info(f"使用默认平台ID: {platform_id}")

    # 准备任务数据
    metadata_dict = task.metadata.model_dump()

    # 转换字段名：contentType -> content_type
    if "contentType" in metadata_dict:
        metadata_dict["content_type"] = metadata_dict.pop("contentType")

    # 转换字段名：titleTemplate -> title_template
    if "titleTemplate" in metadata_dict:
        metadata_dict["title_template"] = metadata_dict.pop("titleTemplate")

    # 转换字段名：privacyStatus -> privacy_status
    if "privacyStatus" in metadata_dict:
        metadata_dict["privacy_status"] = metadata_dict.pop("privacyStatus")

    # 转换字段名：musicLibrary -> music_library
    if "musicLibrary" in metadata_dict:
        metadata_dict["music_library"] = metadata_dict.pop("musicLibrary")

    # 转换字段名：publishStrategy -> publish_strategy
    if "publishStrategy" in metadata_dict:
        metadata_dict["publish_strategy"] = metadata_dict.pop("publishStrategy")

    logger.info(f"转换后的元数据: {metadata_dict}")  # 添加调试日志

    task_data = {
        "task_id": task_id,
        "folder_path": task.folderPath,
        "account_id": task.accountId,
        "platform_id": platform_id,  # 使用从数据库获取的平台ID
        "metadata": metadata_dict,
        "status": "pending",
        "created_at": datetime.datetime.now(),
    }

    # 记录使用的平台ID
    logger.info(f"YouTube上传任务使用平台ID: {platform_id}, 任务ID: {task_id}")

    # 如果提供了选中的文件列表，添加到任务数据中
    if task.selectedFiles:
        task_data["selected_files"] = task.selectedFiles

    try:
        # 创建任务
        task_id = db_service.create_task(task_data)

        # 返回任务ID和状态
        return {
            "task_id": task_id,
            "status": "pending",
            "estimated_time": 3600  # 预计完成时间（秒）
        }
    except Exception as e:
        logger.error(f"创建上传任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建上传任务失败: {str(e)}")

# 获取上传任务列表
@router.get("/uploads", response_model=List[UploadTask])
async def get_upload_tasks(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取YouTube视频上传任务列表

    - **skip**: 跳过的记录数
    - **limit**: 返回的最大记录数
    - **status**: 可选，按状态筛选
    """
    try:
        # 构建查询条件
        query = {}
        if status:
            query["status"] = status

        # 查询任务列表
        tasks = db_service.get_tasks(query, skip, limit)

        # 格式化任务数据
        formatted_tasks = []
        for task in tasks:
            formatted_task = {
                "id": str(task["_id"]),
                "folder_path": task["folder_path"],
                "account_id": task["account_id"],
                "metadata": task["metadata"],
                "status": task["status"],
                "created_at": task["created_at"]
            }

            # 添加可选字段
            if "selected_files" in task:
                formatted_task["selected_files"] = task["selected_files"]
            if "updated_at" in task:
                formatted_task["updated_at"] = task["updated_at"]

            formatted_tasks.append(formatted_task)

        return formatted_tasks
    except Exception as e:
        logger.error(f"获取上传任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取上传任务列表失败: {str(e)}")

# 获取上传任务日志
@router.get("/uploads/{task_id}/logs", response_model=List[UploadTaskLog])
async def get_upload_task_logs(
    task_id: str,
    skip: int = 0,
    limit: int = 100,
    db_service: SocialDatabaseService = Depends(get_social_service)
):
    """
    获取指定上传任务的日志

    - **task_id**: 任务ID
    - **skip**: 跳过的记录数
    - **limit**: 返回的最大记录数
    """
    try:
        # 验证任务是否存在
        task = db_service.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 查询任务日志
        logs = db_service.get_task_logs(task_id, skip, limit)

        # 格式化日志数据
        formatted_logs = []
        for log in logs:
            formatted_log = {
                "task_id": log["task_id"],
                "timestamp": log["timestamp"],
                "message": log["message"],
                "level": log["level"]
            }

            # 添加可选字段
            if "details" in log:
                formatted_log["details"] = log["details"]

            formatted_logs.append(formatted_log)

        return formatted_logs
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务日志失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务日志失败: {str(e)}")
