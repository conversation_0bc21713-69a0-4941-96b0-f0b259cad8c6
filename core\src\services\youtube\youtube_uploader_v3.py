"""
YouTube上传器 V3
基于基类的简化版本
"""

import os
import logging
import asyncio

from ..common.base_uploader import BaseUploader
from ..common.workflow_engine import WorkflowEngine

logger = logging.getLogger(__name__)


class YouTubeUploaderV3(BaseUploader):
    """YouTube上传器V3类"""

    def __init__(self, device_id: str, appium_server: str = 'http://localhost:4723'):
        """初始化YouTube上传器

        Args:
            device_id: 设备ID
            appium_server: Appium服务器地址
        """
        super().__init__(
            device_id=device_id,
            app_package='com.google.android.youtube',
            app_activity='com.google.android.youtube.app.honeycomb.Shell$HomeActivity',
            appium_server=appium_server
        )

        # 初始化配置驱动的元素查找器和工作流引擎
        # 获取项目根目录路径
        current_file = os.path.abspath(__file__)
        logger.info(f"当前文件路径: {current_file}")

        # 从 core/src/services/youtube/youtube_uploader_v3.py 到 core/config/platforms/youtube/
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(current_file))))
        config_dir = os.path.join(project_root, 'config', 'platforms', 'youtube')
        elements_config_path = os.path.join(config_dir, 'elements.yaml')

        logger.info(f"项目根目录: {project_root}")
        logger.info(f"配置目录: {config_dir}")
        logger.info(f"元素配置文件路径: {elements_config_path}")
        logger.info(f"元素配置文件是否存在: {os.path.exists(elements_config_path)}")

        # 初始化工作流引擎
        self.workflow_engine = WorkflowEngine(elements_config_path)
        self.workflow_engine.set_progress_callback(self.update_progress)
        self.workflow_engine.set_status_callback(self.set_status)

        # 工作流配置路径
        self.workflows_dir = os.path.join(config_dir, 'workflows')
        logger.info(f"工作流配置目录: {self.workflows_dir}")
        logger.info(f"工作流配置目录是否存在: {os.path.exists(self.workflows_dir)}")

    def get_app_name(self) -> str:
        """获取应用名称

        Returns:
            str: 应用名称
        """
        return "YouTube"

    async def verify_app_launched(self) -> bool:
        """验证YouTube应用是否已启动

        Returns:
            bool: 是否已启动
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                logger.error("无法获取Appium驱动")
                return False

            logger.info("开始验证YouTube应用启动状态...")

            # 首先尝试重新建立Appium连接，解决AccessibilityNodeInfo超时问题
            await self._fix_appium_connection_if_needed(driver)

            # 方法1：检查当前应用包名（最可靠的方法）
            try:
                current_package = driver.current_package
                logger.info(f"当前应用包名: {current_package}")
                if current_package == "com.google.android.youtube":
                    logger.info("✅ YouTube应用启动成功（通过包名验证）")
                    return True
                else:
                    logger.warning(f"⚠️ 当前应用不是YouTube，而是: {current_package}")
                    # 不立即返回False，尝试其他验证方法
            except Exception as e:
                logger.warning(f"获取当前包名失败: {str(e)}")
                # 不立即返回False，尝试其他验证方法

            # 方法2：检查活动名称作为备用
            try:
                current_activity = driver.current_activity
                logger.info(f"当前活动: {current_activity}")
                if "youtube" in current_activity.lower():
                    logger.info("✅ YouTube应用启动成功（通过活动名验证）")
                    return True
                else:
                    logger.warning(f"⚠️ 当前活动不是YouTube相关: {current_activity}")
            except Exception as e:
                logger.warning(f"获取当前活动失败: {str(e)}")

            # 如果所有验证方法都失败
            logger.error("❌ 所有验证方法都失败，YouTube应用未正确启动")
            return False

        except Exception as e:
            logger.error(f"验证YouTube应用启动状态异常: {str(e)}")
            return False

    async def _fix_appium_connection_if_needed(self, driver) -> bool:
        """修复Appium连接问题（如果需要）

        Args:
            driver: Appium驱动

        Returns:
            bool: 是否修复成功
        """
        try:
            logger.info("检查Appium连接状态...")

            # 尝试一个简单的操作来测试连接
            try:
                _ = driver.current_package
                logger.info("Appium连接正常")
                return True
            except Exception as e:
                logger.warning(f"Appium连接异常: {str(e)}")

                # 如果是AccessibilityNodeInfo超时问题，尝试重启Appium会话
                if "AccessibilityNodeInfo" in str(e) or "Timed out" in str(e):
                    logger.info("检测到AccessibilityNodeInfo超时问题，尝试重新建立连接...")

                    # 重新启动应用
                    try:
                        driver.terminate_app("com.google.android.youtube")
                        await asyncio.sleep(2)
                        driver.activate_app("com.google.android.youtube")
                        await asyncio.sleep(3)

                        # 测试连接是否恢复
                        _ = driver.current_package
                        logger.info("✅ Appium连接已恢复")
                        return True
                    except Exception as restart_e:
                        logger.error(f"重启应用失败: {str(restart_e)}")
                        return False
                else:
                    logger.error(f"未知的Appium连接问题: {str(e)}")
                    return False

        except Exception as e:
            logger.error(f"修复Appium连接异常: {str(e)}")
            return False

    async def execute_upload_task(self, video_path: str, title: str, description: str, privacy: str, content_type: str = "video", selected_music: list = None) -> bool:
        """执行YouTube上传任务

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            privacy: 隐私设置（public, unlisted, private）
            content_type: 内容类型（video, shorts, live, post）
            selected_music: 选中的音乐列表

        Returns:
            bool: 是否上传成功
        """
        try:
            logger.info("===== 开始执行YouTube上传任务 =====")
            logger.info(f"视频路径: {video_path}")
            logger.info(f"视频标题: {title}")
            logger.info(f"隐私设置: {privacy}")
            logger.info(f"内容类型: {content_type}")
            logger.info(f"选中的音乐: {selected_music}")
            logger.info(f"选中音乐数量: {len(selected_music) if selected_music else 0}")

            # 更新上传状态
            self.set_status("uploading", "正在准备上传...")
            self.update_progress(0, "正在准备上传...")

            # 检查文件是否存在
            if not os.path.exists(video_path):
                logger.error(f"视频文件不存在: {video_path}")
                self.set_status("error", f"视频文件不存在: {video_path}")
                return False

            # 启动YouTube应用（包含网络连接检查和V2rayN启动）
            if not await self._launch_youtube_with_network_check():
                return False
            self.update_progress(20, "YouTube应用已启动")

            # 推送视频文件到设备
            if not await self.prepare_media_file(video_path):
                return False
            self.update_progress(30, "视频文件已推送到设备")

            # 执行上传流程
            return await self._perform_youtube_upload(video_path, title, description, privacy, content_type, selected_music)

        except Exception as e:
            logger.error(f"执行上传任务异常: {str(e)}", exc_info=True)
            self.set_status("error", f"执行上传任务异常: {str(e)}")
            return False

    async def launch_app(self) -> bool:
        """重写基类的launch_app方法，使用YouTube特定的网络检查逻辑

        Returns:
            bool: 是否成功启动
        """
        return await self._launch_youtube_with_network_check()

    async def _launch_youtube_with_network_check(self) -> bool:
        """启动YouTube应用（包含网络连接检查和V2rayN启动）

        Returns:
            bool: 是否成功启动
        """
        try:
            logger.info("===== 开始启动YouTube应用 =====")
            logger.info(f"设备ID: {self.device_manager.device_id}")

            # 先检查网络连接
            logger.info("检查网络连接状态...")
            network_status = await self.network_manager.check_network_connection()
            logger.info(f"网络状态: {network_status}")

            # 如果无法访问Google，尝试启动V2rayN（无论是否能访问基础网络）
            if not network_status.get("google_accessible", False):
                if network_status.get("basic_network", False):
                    logger.info("无法访问Google但可以访问基础网络，尝试启动V2rayN...")
                    self.upload_message = "网络连接受限，正在启动V2rayN代理..."
                else:
                    logger.info("无法访问Google和基础网络，尝试启动V2rayN...")
                    self.upload_message = "网络连接异常，正在启动V2rayN代理..."

                # 启动V2rayN
                v2ray_success = await self.v2ray_manager.launch_and_connect()
                if v2ray_success:
                    logger.info("V2rayN启动成功，等待代理生效...")
                    await asyncio.sleep(5)  # 等待代理生效

                    # 重新检查Google连接
                    logger.info("重新检查Google连接...")
                    network_status = await self.network_manager.check_network_connection()
                    logger.info(f"启动V2rayN后的网络状态: {network_status}")

                    if network_status.get("google_accessible", False):
                        logger.info("启动V2rayN后可以访问Google，网络连接正常")
                        self.upload_message = "V2rayN启动成功，网络连接正常"
                    else:
                        logger.warning("启动V2rayN后仍无法访问Google，但继续执行")
                        self.upload_message = "V2rayN已启动，但网络连接仍受限，继续执行"

                        # 再次尝试启动V2rayN
                        logger.info("再次尝试启动V2rayN...")
                        await asyncio.sleep(5)
                        v2ray_success = await self.v2ray_manager.launch_and_connect()
                        if v2ray_success:
                            await asyncio.sleep(3)
                            # 第三次检查Google连接
                            network_status = await self.network_manager.check_network_connection()
                            if network_status.get("google_accessible", False):
                                logger.info("第二次启动V2rayN后可以访问Google")
                                self.upload_message = "V2rayN启动成功，网络连接正常"
                            else:
                                logger.warning("多次尝试后仍无法访问Google，但继续执行")
                                self.upload_message = "网络连接受限，但继续执行"
                else:
                    logger.warning("V2rayN启动失败，但继续执行")
                    self.upload_message = "V2rayN启动失败，网络连接受限，但继续执行"
            else:
                logger.info("网络连接正常，可以访问Google")
                self.upload_message = "网络连接正常"

            # 确保网络连接检查和V2rayN启动已完成
            logger.info("网络连接检查和V2rayN启动已完成，现在启动YouTube应用")
            await asyncio.sleep(3)  # 等待一段时间确保V2rayN生效

            # 启动YouTube应用
            success = await self.device_manager.launch_app(self.app_package, self.app_activity)
            if not success:
                logger.error("启动YouTube应用失败")
                self.set_status("error", "启动YouTube应用失败")
                return False

            # 验证应用是否成功启动
            if not await self.verify_app_launched():
                logger.error("YouTube应用未成功启动")
                self.set_status("error", "YouTube应用未成功启动")
                return False

            logger.info("YouTube应用已成功启动")
            self.upload_message = "YouTube应用已启动"
            return True

        except Exception as e:
            logger.error(f"启动YouTube应用异常: {str(e)}", exc_info=True)
            self.set_status("error", f"启动YouTube应用异常: {str(e)}")
            return False

    async def _perform_youtube_upload(self, video_path: str, title: str, description: str, privacy: str, content_type: str = "video", selected_music: list = None) -> bool:
        """执行YouTube上传流程（工作流驱动版本）

        Args:
            video_path: 视频文件路径
            title: 视频标题
            description: 视频描述
            privacy: 隐私设置
            content_type: 内容类型（video, shorts, live, post）
            selected_music: 选中的音乐列表

        Returns:
            bool: 是否上传成功
        """
        try:
            driver = self.device_manager.get_driver()
            if not driver:
                self.set_status("error", "没有可用的Appium驱动")
                return False

            logger.info("🚀 开始执行YouTube上传流程（工作流驱动版本）...")
            logger.info(f"📋 接收到的参数 - content_type: '{content_type}', type: {type(content_type)}")
            self.update_progress(35, "正在执行上传流程...")

            # 根据内容类型选择工作流
            if content_type == "shorts":
                workflow_file = "shorts_upload.yaml"
                logger.info("✅ 选择短视频工作流")
            else:
                workflow_file = "video_upload.yaml"
                logger.info(f"✅ 选择普通视频工作流（content_type='{content_type}'）")

            workflow_path = os.path.join(self.workflows_dir, workflow_file)
            logger.info(f"使用工作流配置: {workflow_path}")
            logger.info(f"工作流文件是否存在: {os.path.exists(workflow_path)}")

            # 加载工作流
            if not self.workflow_engine.load_workflow(workflow_path):
                logger.error(f"❌ 加载工作流失败: {workflow_path}")
                self.set_status("error", "加载工作流配置失败")
                return False

            # 准备工作流参数
            video_filename = os.path.basename(video_path)
            workflow_parameters = {
                'filename': video_filename,
                'title': title,
                'description': description,
                'privacy': privacy,
                'selectedMusic': selected_music or []  # 传递音乐配置
            }

            logger.info(f"工作流参数: {workflow_parameters}")

            # 执行工作流
            success = await self.workflow_engine.execute_workflow(driver, **workflow_parameters)

            if success:
                logger.info("🎉 工作流执行成功")
                return True
            else:
                logger.error("❌ 工作流执行失败")
                return False

        except Exception as e:
            logger.error(f"执行YouTube上传流程异常: {str(e)}", exc_info=True)
            self.set_status("error", f"执行YouTube上传流程异常: {str(e)}")
            return False