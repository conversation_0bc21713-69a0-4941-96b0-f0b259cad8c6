"""
工作流引擎
支持配置文件驱动的自动化工作流执行
"""

import asyncio
import logging
import yaml
from typing import Dict, Any, Optional, Callable

from .element_finder import ElementFinder

logger = logging.getLogger(__name__)


class WorkflowEngine:
    """配置驱动的工作流引擎"""

    def __init__(self, elements_config_path: str):
        """初始化工作流引擎

        Args:
            elements_config_path: 元素配置文件路径
        """
        self.element_finder = ElementFinder(elements_config_path)
        self.current_workflow = None
        self.current_step_index = 0
        self.workflow_context = {}
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback

    def load_workflow(self, workflow_path: str) -> bool:
        """加载工作流配置

        Args:
            workflow_path: 工作流配置文件路径

        Returns:
            bool: 是否加载成功
        """
        try:
            logger.info(f"加载工作流配置: {workflow_path}")
            with open(workflow_path, 'r', encoding='utf-8') as f:
                self.current_workflow = yaml.safe_load(f)

            workflow_info = self.current_workflow.get('workflow', {})
            logger.info(f"✅ 成功加载工作流: {workflow_info.get('name', 'Unknown')}")
            logger.info(f"工作流描述: {workflow_info.get('description', 'No description')}")
            logger.info(f"工作流版本: {workflow_info.get('version', 'Unknown')}")
            steps = workflow_info.get('steps', [])
            logger.info(f"工作流步骤数: {len(steps)}")

            # 打印所有步骤的名称以便调试
            for i, step in enumerate(steps):
                logger.info(f"  步骤{i+1}: {step.get('name', 'Unknown')} (id: {step.get('id', 'Unknown')}, action: {step.get('action', 'Unknown')})")

            self.current_step_index = 0
            self.workflow_context = {}
            return True

        except Exception as e:
            logger.error(f"❌ 加载工作流配置失败: {workflow_path}, 错误: {e}")
            return False

    async def execute_workflow(self, driver, **parameters) -> bool:
        """执行工作流

        Args:
            driver: Appium驱动
            **parameters: 工作流参数

        Returns:
            bool: 是否执行成功
        """
        if not self.current_workflow:
            logger.error("❌ 没有加载工作流配置")
            return False

        try:
            workflow_info = self.current_workflow['workflow']
            steps = workflow_info.get('steps', [])
            config = self.current_workflow.get('config', {})

            logger.info(f"🚀 开始执行工作流: {workflow_info.get('name')}")
            logger.info(f"总步骤数: {len(steps)}")

            # 更新工作流上下文
            self.workflow_context.update(parameters)

            # 执行每个步骤
            for i, step in enumerate(steps):
                self.current_step_index = i
                step_name = step.get('name', f'Step {i+1}')
                step_id = step.get('id', 'Unknown')
                step_action = step.get('action', 'Unknown')
                step_required = step.get('required', True)

                logger.info(f"📋 执行步骤 {i+1}/{len(steps)}: {step_name}")
                logger.info(f"🔧 步骤详情 - ID: {step_id}, 动作: {step_action}, 必需: {step_required}")

                # 更新进度
                progress = int((i / len(steps)) * 100)
                self._update_progress(progress, f"执行步骤: {step_name}")

                # 执行步骤
                try:
                    success = await self._execute_step(driver, step, config)
                    logger.info(f"🔍 步骤执行结果: {step_name} -> {'成功' if success else '失败'}")

                    if not success:
                        if step_required:
                            logger.error(f"❌ 必需步骤失败: {step_name} (ID: {step_id})")
                            self._update_status("error", f"步骤失败: {step_name}")
                            return False
                        else:
                            logger.warning(f"⚠️ 可选步骤失败，继续执行: {step_name} (ID: {step_id})")
                            continue

                    logger.info(f"✅ 步骤完成: {step_name}")
                except Exception as step_error:
                    logger.error(f"❌ 步骤执行异常: {step_name} (ID: {step_id}) - {str(step_error)}")
                    if step_required:
                        self._update_status("error", f"步骤异常: {step_name}")
                        return False
                    else:
                        logger.warning(f"⚠️ 可选步骤异常，继续执行: {step_name}")
                        continue

                # 等待
                wait_time = step.get('wait_after', 0)
                if wait_time > 0:
                    logger.info(f"⏳ 等待 {wait_time} 秒...")
                    await asyncio.sleep(wait_time)

            # 工作流完成
            logger.info("🎉 工作流执行完成")
            self._update_progress(100, "工作流执行完成")
            self._update_status("completed", "工作流执行成功")
            return True

        except Exception as e:
            logger.error(f"❌ 工作流执行异常: {str(e)}", exc_info=True)
            self._update_status("error", f"工作流执行异常: {str(e)}")
            return False

    async def _execute_step(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行单个步骤

        Args:
            driver: Appium驱动
            step: 步骤配置
            config: 工作流配置

        Returns:
            bool: 是否执行成功
        """
        try:
            action = step.get('action')
            element_name = step.get('element')
            step_id = step.get('id')

            logger.info(f"🔧 执行动作: {action}")

            if action == "click":
                return await self._execute_click_action(driver, step)
            elif action == "click_if_exists":
                return await self._execute_click_if_exists_action(driver, step)
            elif action == "input_text":
                return await self._execute_input_action(driver, step)
            elif action == "select_option":
                return await self._execute_select_action(driver, step)
            elif action == "wait_for_completion":
                return await self._execute_wait_action(driver, step)
            elif action == "wait_for_element":
                return await self._execute_wait_for_element_action(driver, step)
            elif action == "select_music":
                return await self._execute_select_music_action(driver, step, config)
            else:
                logger.error(f"❌ 不支持的动作类型: {action}")
                return False

        except Exception as e:
            logger.error(f"❌ 执行步骤异常: {str(e)}")
            return False

    async def _execute_click_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行点击动作"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)

        return await self.element_finder.find_and_click(driver, element_name, **parameters)

    async def _execute_click_if_exists_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行条件点击动作（如果元素存在则点击）"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)
        step_name = step.get('name', 'Unknown')

        logger.info(f"🔍 检查元素是否存在: {element_name}")

        try:
            # 尝试查找元素，但不要求必须找到
            element = await self.element_finder.find_element(driver, element_name, **parameters)
            if element:
                # 如果找到元素，则点击
                success = await self.element_finder.find_and_click(driver, element_name, **parameters)
                if success:
                    logger.info(f"✅ 元素存在并成功点击: {step_name}")
                    return True
                else:
                    logger.warning(f"⚠️ 元素存在但点击失败: {step_name}")
                    return False
            else:
                # 如果没有找到元素，这是正常情况（可选步骤）
                logger.info(f"ℹ️ 元素不存在，跳过步骤: {step_name}")
                return True

        except Exception as e:
            # 对于可选步骤，异常也被视为正常情况
            logger.info(f"ℹ️ 元素不存在或查找异常，跳过步骤: {step_name} - {str(e)}")
            return True

    async def _execute_input_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行输入动作"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)

        # 获取要输入的文本
        text_param = None
        for param in step.get('parameters', []):
            param_name = param.get('name')
            if param_name in self.workflow_context:
                text_param = self.workflow_context[param_name]
                break

        if text_param is None:
            logger.warning(f"⚠️ 没有找到输入文本参数")
            return True  # 对于可选的输入，返回True

        return await self.element_finder.input_text(driver, element_name, text_param)

    async def _execute_select_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行选择动作"""
        # 这里可以实现隐私选项选择逻辑
        # 暂时返回True，后续可以扩展
        logger.info("🔧 执行选择动作（暂未实现）")
        return True

    async def _execute_wait_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行等待动作"""
        step_id = step.get('id')
        timeout = step.get('timeout', 60)
        description = step.get('description', 'Unknown')

        logger.info(f"🔧 执行等待动作: {description}")

        if step_id == "wait_upload":
            # 等待上传完成的逻辑
            logger.info(f"等待上传完成，最大等待时间: {timeout}秒")
            await asyncio.sleep(timeout)  # 简化为固定等待时间
            logger.info("✅ 上传等待完成")
            return True
        elif step_id == "wait_video_processing":
            # 等待视频处理完成的逻辑
            logger.info(f"⏳ 等待YouTube视频处理完成，等待时间: {timeout}秒")
            logger.info("YouTube正在处理视频，请耐心等待...")

            # 分段等待，每5秒报告一次进度
            total_waited = 0
            while total_waited < timeout:
                wait_chunk = min(5, timeout - total_waited)
                await asyncio.sleep(wait_chunk)
                total_waited += wait_chunk

                progress = int((total_waited / timeout) * 100)
                logger.info(f"视频处理进度: {total_waited}/{timeout}秒 ({progress}%)")

                # 更新进度回调
                if self.progress_callback:
                    self.progress_callback(50 + int(progress * 0.1), f"等待视频处理: {progress}%")

            logger.info("✅ 视频处理等待完成")
            return True
        else:
            # 通用等待逻辑
            wait_time = min(timeout, 10)  # 最多等待10秒
            logger.info(f"等待 {wait_time} 秒...")
            await asyncio.sleep(wait_time)
            logger.info("✅ 等待完成")
            return True

    async def _execute_wait_for_element_action(self, driver, step: Dict[str, Any]) -> bool:
        """等待元素出现"""
        element_name = step.get('element')
        timeout = step.get('timeout', 30)
        description = step.get('description', 'Unknown')

        logger.info(f"🔍 等待元素出现: {element_name}")
        logger.info(f"⏳ 最大等待时间: {timeout}秒")
        logger.info(f"📋 等待描述: {description}")

        start_time = asyncio.get_event_loop().time()
        check_interval = 5  # 每5秒检查一次
        last_progress_report = 0  # 上次进度报告的时间

        while True:
            try:
                # 尝试查找真实元素（跳过坐标定位）
                element = await self.element_finder.find_element(driver, element_name, wait_for_real_element=True)
                if element:
                    elapsed_time = int(asyncio.get_event_loop().time() - start_time)
                    logger.info(f"✅ 元素已出现: {element_name} (等待了 {elapsed_time} 秒)")
                    return True

                # 检查是否超时
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time >= timeout:
                    logger.error(f"❌ 等待元素超时: {element_name} (等待了 {int(elapsed_time)} 秒)")
                    return False

                # 等待一段时间再检查
                progress = int((elapsed_time / timeout) * 100)

                # 每30秒报告一次进度，避免日志过于频繁
                if elapsed_time - last_progress_report >= 30:
                    logger.info(f"🔍 继续等待元素出现: {element_name} ({int(elapsed_time)}/{timeout}秒, {progress}%)")
                    last_progress_report = elapsed_time

                    # 更新进度回调
                    if self.progress_callback:
                        self.progress_callback(50 + int(progress * 0.1), f"等待元素出现: {progress}%")

                await asyncio.sleep(check_interval)

            except Exception as e:
                # 元素未找到是正常情况，继续等待
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time >= timeout:
                    logger.error(f"❌ 等待元素超时: {element_name} - {str(e)}")
                    return False

                await asyncio.sleep(check_interval)

    async def _execute_select_music_action(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行音乐选择动作"""
        try:
            # 从配置中获取选中的音乐
            selected_music = config.get('selectedMusic', [])

            if not selected_music:
                logger.info("ℹ️ 未选择音乐，跳过音乐选择步骤")
                return True

            logger.info(f"🎵 开始选择音乐，共 {len(selected_music)} 首")

            # 如果选择了多首音乐，随机选择一首
            import random
            if len(selected_music) > 1:
                music = random.choice(selected_music)
                logger.info(f"🎲 从 {len(selected_music)} 首音乐中随机选择了: {music.get('title', '')} ({music.get('music_id', '')})")
            else:
                music = selected_music[0]
                logger.info(f"🎵 选择唯一的音乐: {music.get('title', '')} ({music.get('music_id', '')})")

            music_title = music.get('title', '')
            music_id = music.get('music_id', '')

            logger.info(f"🎵 最终选择的音乐: {music_title} ({music_id})")

            # 点击搜索框
            logger.info("🔍 点击音乐搜索框")
            search_success = await self.element_finder.find_and_click(driver, "music_search_box")
            if not search_success:
                logger.error("❌ 无法点击音乐搜索框")
                return False

            await asyncio.sleep(2)

            # 输入搜索关键词（优先使用音乐ID，其次使用标题）
            search_keyword = music_id if music_id else music_title
            logger.info(f"🔍 搜索音乐: {search_keyword}")

            input_success = await self.element_finder.input_text(driver, "music_search_input", search_keyword)
            if not input_success:
                logger.error("❌ 无法输入搜索关键词")
                return False

            await asyncio.sleep(3)  # 等待搜索结果

            # 尝试点击搜索结果
            logger.info("🎵 尝试选择搜索结果")

            # 先尝试通过音乐标题查找
            if music_title:
                music_found = await self.element_finder.find_and_click(
                    driver, "music_item", music_title=music_title
                )
                if music_found:
                    logger.info(f"✅ 成功选择音乐: {music_title}")
                    return True

            # 再尝试通过音乐ID查找
            if music_id:
                music_found = await self.element_finder.find_and_click(
                    driver, "music_item", music_id=music_id
                )
                if music_found:
                    logger.info(f"✅ 成功选择音乐: {music_id}")
                    return True

            logger.warning(f"⚠️ 未找到指定音乐: {search_keyword}")
            return False

        except Exception as e:
            logger.error(f"❌ 音乐选择异常: {str(e)}")
            return False

    def _get_step_parameters(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """获取步骤参数"""
        parameters = {}
        for param in step.get('parameters', []):
            param_name = param.get('name')
            if param_name in self.workflow_context:
                parameters[param_name] = self.workflow_context[param_name]
        return parameters

    def _update_progress(self, progress: int, message: str):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, message)

    def _update_status(self, status: str, message: str):
        """更新状态"""
        if self.status_callback:
            self.status_callback(status, message)
