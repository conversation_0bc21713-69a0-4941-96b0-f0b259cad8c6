"""
工作流引擎
支持配置文件驱动的自动化工作流执行
"""

import asyncio
import logging
import yaml
from typing import Dict, Any, Optional, Callable

from .element_finder import ElementFinder

# 导入Keys用于键盘操作
try:
    from selenium.webdriver.common.keys import Keys
except ImportError:
    # 如果无法导入，定义一个简单的替代
    class Keys:
        ENTER = "\u000D"
        RETURN = "\u000D"
        BACKSPACE = "\u0008"

# 导入Android键盘操作
try:
    from appium.webdriver.common.appiumby import AppiumBy
    from appium.webdriver.extensions.android.nativekey import AndroidKey
except ImportError:
    # 如果无法导入，定义一个简单的替代
    class AndroidKey:
        ENTER = 66
        SEARCH = 84
        BACK = 4
        DEL = 67

logger = logging.getLogger(__name__)


class WorkflowEngine:
    """配置驱动的工作流引擎"""

    def __init__(self, elements_config_path: str):
        """初始化工作流引擎

        Args:
            elements_config_path: 元素配置文件路径
        """
        self.element_finder = ElementFinder(elements_config_path)
        self.current_workflow = None
        self.current_step_index = 0
        self.workflow_context = {}
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None

    def set_progress_callback(self, callback: Callable):
        """设置进度回调函数"""
        self.progress_callback = callback

    def set_status_callback(self, callback: Callable):
        """设置状态回调函数"""
        self.status_callback = callback

    async def _send_enter_key_via_adb(self, driver, method_name="") -> bool:
        """使用ADB发送回车键"""
        try:
            # 获取设备ID
            device_id = getattr(driver, 'desired_capabilities', {}).get('udid') or getattr(driver, 'capabilities', {}).get('udid')
            if not device_id:
                # 尝试通过adb devices获取设备ID
                try:
                    import subprocess
                    result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                        for line in lines:
                            if 'device' in line:
                                device_id = line.split()[0]
                                break
                except:
                    pass

            if device_id:
                logger.info(f"🔍 {method_name}: 使用ADB发送回车键到设备: {device_id}")
                import subprocess
                adb_cmd = ['adb', '-s', device_id, 'shell', 'input', 'keyevent', '66']
                result = subprocess.run(adb_cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    logger.info(f"✅ {method_name}: ADB回车键发送成功")
                    return True
                else:
                    logger.warning(f"⚠️ {method_name}: ADB回车键发送失败: {result.stderr}")
            else:
                logger.warning(f"⚠️ {method_name}: 无法获取设备ID，跳过ADB方法")

        except Exception as adb_e:
            logger.warning(f"⚠️ {method_name}: ADB方法失败: {str(adb_e)}")

        return False

    def load_workflow(self, workflow_path: str) -> bool:
        """加载工作流配置

        Args:
            workflow_path: 工作流配置文件路径

        Returns:
            bool: 是否加载成功
        """
        try:
            logger.info(f"加载工作流配置: {workflow_path}")
            with open(workflow_path, 'r', encoding='utf-8') as f:
                self.current_workflow = yaml.safe_load(f)

            workflow_info = self.current_workflow.get('workflow', {})
            logger.info(f"✅ 成功加载工作流: {workflow_info.get('name', 'Unknown')}")
            logger.info(f"工作流描述: {workflow_info.get('description', 'No description')}")
            logger.info(f"工作流版本: {workflow_info.get('version', 'Unknown')}")
            steps = workflow_info.get('steps', [])
            logger.info(f"工作流步骤数: {len(steps)}")

            # 打印所有步骤的名称以便调试
            for i, step in enumerate(steps):
                logger.info(f"  步骤{i+1}: {step.get('name', 'Unknown')} (id: {step.get('id', 'Unknown')}, action: {step.get('action', 'Unknown')})")

            self.current_step_index = 0
            self.workflow_context = {}
            return True

        except Exception as e:
            logger.error(f"❌ 加载工作流配置失败: {workflow_path}, 错误: {e}")
            return False

    async def execute_workflow(self, driver, **parameters) -> bool:
        """执行工作流

        Args:
            driver: Appium驱动
            **parameters: 工作流参数

        Returns:
            bool: 是否执行成功
        """
        if not self.current_workflow:
            logger.error("❌ 没有加载工作流配置")
            return False

        try:
            workflow_info = self.current_workflow['workflow']
            steps = workflow_info.get('steps', [])
            config = self.current_workflow.get('config', {})

            logger.info(f"🚀 开始执行工作流: {workflow_info.get('name')}")
            logger.info(f"总步骤数: {len(steps)}")

            # 更新工作流上下文
            self.workflow_context.update(parameters)

            # 执行每个步骤
            for i, step in enumerate(steps):
                self.current_step_index = i
                step_name = step.get('name', f'Step {i+1}')
                step_id = step.get('id', 'Unknown')
                step_action = step.get('action', 'Unknown')
                step_required = step.get('required', True)

                logger.info(f"📋 执行步骤 {i+1}/{len(steps)}: {step_name}")
                logger.info(f"🔧 步骤详情 - ID: {step_id}, 动作: {step_action}, 必需: {step_required}")

                # 更新进度
                progress = int((i / len(steps)) * 100)
                self._update_progress(progress, f"执行步骤: {step_name}")

                # 执行步骤
                try:
                    success = await self._execute_step(driver, step, config)
                    logger.info(f"🔍 步骤执行结果: {step_name} -> {'成功' if success else '失败'}")

                    if not success:
                        if step_required:
                            logger.error(f"❌ 必需步骤失败: {step_name} (ID: {step_id})")
                            self._update_status("error", f"步骤失败: {step_name}")
                            return False
                        else:
                            logger.warning(f"⚠️ 可选步骤失败，继续执行: {step_name} (ID: {step_id})")
                            continue

                    logger.info(f"✅ 步骤完成: {step_name}")
                except Exception as step_error:
                    logger.error(f"❌ 步骤执行异常: {step_name} (ID: {step_id}) - {str(step_error)}")
                    if step_required:
                        self._update_status("error", f"步骤异常: {step_name}")
                        return False
                    else:
                        logger.warning(f"⚠️ 可选步骤异常，继续执行: {step_name}")
                        continue

                # 等待
                wait_time = step.get('wait_after', 0)
                if wait_time > 0:
                    logger.info(f"⏳ 等待 {wait_time} 秒...")
                    await asyncio.sleep(wait_time)

            # 工作流完成
            logger.info("🎉 工作流执行完成")
            self._update_progress(100, "工作流执行完成")
            self._update_status("completed", "工作流执行成功")
            return True

        except Exception as e:
            logger.error(f"❌ 工作流执行异常: {str(e)}", exc_info=True)
            self._update_status("error", f"工作流执行异常: {str(e)}")
            return False

    async def _execute_step(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行单个步骤

        Args:
            driver: Appium驱动
            step: 步骤配置
            config: 工作流配置

        Returns:
            bool: 是否执行成功
        """
        try:
            action = step.get('action')
            element_name = step.get('element')
            step_id = step.get('id')

            logger.info(f"🔧 执行动作: {action}")

            if action == "click":
                return await self._execute_click_action(driver, step)
            elif action == "click_if_exists":
                return await self._execute_click_if_exists_action(driver, step)
            elif action == "input_text":
                return await self._execute_input_action(driver, step)
            elif action == "select_option":
                return await self._execute_select_action(driver, step)
            elif action == "wait_for_completion":
                return await self._execute_wait_action(driver, step)
            elif action == "wait_for_element":
                return await self._execute_wait_for_element_action(driver, step)
            elif action == "select_music":
                return await self._execute_select_music_action(driver, step, config)
            elif action == "handle_music":
                return await self._execute_handle_music_action(driver, step, config)
            else:
                logger.error(f"❌ 不支持的动作类型: {action}")
                return False

        except Exception as e:
            logger.error(f"❌ 执行步骤异常: {str(e)}")
            return False

    async def _execute_click_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行点击动作"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)

        return await self.element_finder.find_and_click(driver, element_name, **parameters)

    async def _execute_click_if_exists_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行条件点击动作（如果元素存在则点击）"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)
        step_name = step.get('name', 'Unknown')

        logger.info(f"🔍 检查元素是否存在: {element_name}")

        try:
            # 尝试查找元素，但不要求必须找到
            element = await self.element_finder.find_element(driver, element_name, **parameters)
            if element:
                # 如果找到元素，则点击
                success = await self.element_finder.find_and_click(driver, element_name, **parameters)
                if success:
                    logger.info(f"✅ 元素存在并成功点击: {step_name}")
                    return True
                else:
                    logger.warning(f"⚠️ 元素存在但点击失败: {step_name}")
                    return False
            else:
                # 如果没有找到元素，这是正常情况（可选步骤）
                logger.info(f"ℹ️ 元素不存在，跳过步骤: {step_name}")
                return True

        except Exception as e:
            # 对于可选步骤，异常也被视为正常情况
            logger.info(f"ℹ️ 元素不存在或查找异常，跳过步骤: {step_name} - {str(e)}")
            return True

    async def _execute_input_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行输入动作"""
        element_name = step.get('element')
        parameters = self._get_step_parameters(step)

        # 获取要输入的文本
        text_param = None
        for param in step.get('parameters', []):
            param_name = param.get('name')
            if param_name in self.workflow_context:
                text_param = self.workflow_context[param_name]
                break

        if text_param is None:
            logger.warning(f"⚠️ 没有找到输入文本参数")
            return True  # 对于可选的输入，返回True

        return await self.element_finder.input_text(driver, element_name, text_param)

    async def _execute_select_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行选择动作"""
        # 这里可以实现隐私选项选择逻辑
        # 暂时返回True，后续可以扩展
        logger.info("🔧 执行选择动作（暂未实现）")
        return True

    async def _execute_wait_action(self, driver, step: Dict[str, Any]) -> bool:
        """执行等待动作"""
        step_id = step.get('id')
        timeout = step.get('timeout', 60)
        description = step.get('description', 'Unknown')

        logger.info(f"🔧 执行等待动作: {description}")

        if step_id == "wait_upload":
            # 等待上传完成的逻辑
            logger.info(f"等待上传完成，最大等待时间: {timeout}秒")
            await asyncio.sleep(timeout)  # 简化为固定等待时间
            logger.info("✅ 上传等待完成")
            return True
        elif step_id == "wait_video_processing":
            # 等待视频处理完成的逻辑
            logger.info(f"⏳ 等待YouTube视频处理完成，等待时间: {timeout}秒")
            logger.info("YouTube正在处理视频，请耐心等待...")

            # 分段等待，每5秒报告一次进度
            total_waited = 0
            while total_waited < timeout:
                wait_chunk = min(5, timeout - total_waited)
                await asyncio.sleep(wait_chunk)
                total_waited += wait_chunk

                progress = int((total_waited / timeout) * 100)
                logger.info(f"视频处理进度: {total_waited}/{timeout}秒 ({progress}%)")

                # 更新进度回调
                if self.progress_callback:
                    self.progress_callback(50 + int(progress * 0.1), f"等待视频处理: {progress}%")

            logger.info("✅ 视频处理等待完成")
            return True
        else:
            # 通用等待逻辑
            wait_time = min(timeout, 10)  # 最多等待10秒
            logger.info(f"等待 {wait_time} 秒...")
            await asyncio.sleep(wait_time)
            logger.info("✅ 等待完成")
            return True

    async def _execute_wait_for_element_action(self, driver, step: Dict[str, Any]) -> bool:
        """等待元素出现"""
        element_name = step.get('element')
        timeout = step.get('timeout', 30)
        description = step.get('description', 'Unknown')

        logger.info(f"🔍 等待元素出现: {element_name}")
        logger.info(f"⏳ 最大等待时间: {timeout}秒")
        logger.info(f"📋 等待描述: {description}")

        start_time = asyncio.get_event_loop().time()
        check_interval = 5  # 每5秒检查一次
        last_progress_report = 0  # 上次进度报告的时间

        while True:
            try:
                # 尝试查找真实元素（跳过坐标定位）
                element = await self.element_finder.find_element(driver, element_name, wait_for_real_element=True)
                if element:
                    elapsed_time = int(asyncio.get_event_loop().time() - start_time)
                    logger.info(f"✅ 元素已出现: {element_name} (等待了 {elapsed_time} 秒)")
                    return True

                # 检查是否超时
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time >= timeout:
                    logger.error(f"❌ 等待元素超时: {element_name} (等待了 {int(elapsed_time)} 秒)")
                    return False

                # 等待一段时间再检查
                progress = int((elapsed_time / timeout) * 100)

                # 每30秒报告一次进度，避免日志过于频繁
                if elapsed_time - last_progress_report >= 30:
                    logger.info(f"🔍 继续等待元素出现: {element_name} ({int(elapsed_time)}/{timeout}秒, {progress}%)")
                    last_progress_report = elapsed_time

                    # 更新进度回调
                    if self.progress_callback:
                        self.progress_callback(50 + int(progress * 0.1), f"等待元素出现: {progress}%")

                await asyncio.sleep(check_interval)

            except Exception as e:
                # 元素未找到是正常情况，继续等待
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time >= timeout:
                    logger.error(f"❌ 等待元素超时: {element_name} - {str(e)}")
                    return False

                await asyncio.sleep(check_interval)

    async def _execute_select_music_action(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行音乐选择动作"""
        try:
            # 从工作流上下文中获取选中的音乐
            selected_music = self.workflow_context.get('selectedMusic', [])

            if not selected_music:
                logger.info("ℹ️ 未选择音乐，跳过音乐选择步骤")
                return True

            logger.info(f"🎵 开始选择音乐，共 {len(selected_music)} 首")

            # 如果选择了多首音乐，随机选择一首
            import random
            if len(selected_music) > 1:
                music = random.choice(selected_music)
                logger.info(f"🎲 从 {len(selected_music)} 首音乐中随机选择了: {music.get('title', '')} ({music.get('music_id', '')})")
            else:
                music = selected_music[0]
                logger.info(f"🎵 选择唯一的音乐: {music.get('title', '')} ({music.get('music_id', '')})")

            music_title = music.get('title', '')
            music_id = music.get('music_id', '')

            logger.info(f"🎵 最终选择的音乐: {music_title} ({music_id})")

            # 点击搜索框
            logger.info("🔍 点击音乐搜索框")
            search_success = await self.element_finder.find_and_click(driver, "music_search_box")
            if not search_success:
                logger.error("❌ 无法点击音乐搜索框")
                return False

            await asyncio.sleep(2)

            # 输入搜索关键词并直接按回车（一步完成）
            search_keyword = music_id if music_id else music_title
            logger.info(f"🔍 搜索音乐: {search_keyword}")

            # 检查驱动状态，如果有问题则尝试恢复
            try:
                # 简单的驱动状态检查
                current_activity = driver.current_activity
                logger.info(f"🔍 当前活动: {current_activity}")
            except Exception as driver_check_e:
                logger.warning(f"⚠️ 驱动状态检查失败: {str(driver_check_e)}")
                logger.info("🔧 尝试恢复驱动连接...")
                try:
                    # 尝试重新获取页面源码来"唤醒"驱动
                    driver.page_source
                    logger.info("✅ 驱动连接已恢复")
                except Exception as recovery_e:
                    logger.error(f"❌ 驱动恢复失败: {str(recovery_e)}")
                    return False

            # 方法1：使用标准的send_keys + Keys.ENTER（推荐）
            search_success = False
            try:
                logger.info("🔍 方法1: 使用send_keys + Keys.ENTER")
                search_input = await self.element_finder.find_element(driver, "music_search_input")
                if search_input:
                    # 先清空输入框
                    search_input.clear()
                    await asyncio.sleep(0.5)

                    # 一步完成输入+回车（使用Keys.ENTER）
                    search_input.send_keys(search_keyword + Keys.ENTER)
                    logger.info(f"✅ 成功使用Keys.ENTER输入并搜索: {search_keyword}")

                    # 等待一下，检查是否有搜索效果
                    await asyncio.sleep(3)

                    # 检查是否真的有搜索结果
                    try:
                        # 尝试查找音乐项来验证搜索是否生效
                        music_items = driver.find_elements("xpath", "//*[contains(@text, 'KSA') or contains(@content-desc, 'KSA')]")
                        if music_items and len(music_items) > 0:
                            logger.info(f"✅ 检测到 {len(music_items)} 个可能的搜索结果")
                            search_success = True
                        else:
                            logger.warning("⚠️ 未检测到音乐搜索结果，尝试其他方法")
                            # 检查页面源码
                            page_source = driver.page_source
                            if search_keyword in page_source:
                                logger.info("✅ 在页面源码中找到搜索关键词")
                                search_success = True
                            else:
                                logger.warning("⚠️ 页面源码中也没有搜索关键词")
                    except Exception as check_e:
                        logger.warning(f"⚠️ 无法检查搜索效果: {str(check_e)}")
                        # 如果检查失败，不认为搜索成功，继续尝试其他方法

                else:
                    logger.warning("⚠️ 无法找到搜索输入框")
            except Exception as e:
                logger.warning(f"⚠️ Keys.ENTER方法失败: {str(e)}")

            # 方法2：尝试强力搜索触发方式
            if not search_success:
                try:
                    logger.info("🔍 方法2: 尝试强力搜索触发方式")
                    search_input = await self.element_finder.find_element(driver, "music_search_input")
                    if search_input:
                        # 确保输入框有焦点
                        search_input.click()
                        await asyncio.sleep(0.5)

                        # 方式1：使用现代的Android键盘方法
                        logger.info("⌨️ 尝试现代Android键盘方法...")
                        try:
                            # 使用driver.press_key(AndroidKey.ENTER) - 推荐方法
                            if hasattr(driver, 'press_key'):
                                driver.press_key(AndroidKey.ENTER)
                                await asyncio.sleep(1)
                                logger.info("✅ 使用driver.press_key(AndroidKey.ENTER)发送回车键")
                            else:
                                logger.warning("⚠️ 驱动不支持press_key方法")
                                raise AttributeError("Driver does not support press_key")
                        except Exception as android_key_e:
                            logger.warning(f"⚠️ AndroidKey方法失败: {str(android_key_e)}")
                            try:
                                # 备用方法：直接在元素上发送回车
                                search_input.send_keys(Keys.ENTER)
                                await asyncio.sleep(1)
                                logger.info("✅ 直接在元素上发送回车键")
                            except Exception as direct_e:
                                logger.warning(f"⚠️ 直接发送回车也失败: {str(direct_e)}")

                        # 方式2：尝试点击搜索按钮
                        logger.info("🔍 尝试查找搜索按钮...")
                        try:
                            # 查找可能的搜索按钮
                            search_buttons = [
                                "//android.widget.Button[contains(@text, '搜索')]",
                                "//android.widget.Button[contains(@content-desc, 'search')]",
                                "//android.widget.ImageButton[contains(@content-desc, 'search')]",
                                "//android.widget.ImageView[contains(@content-desc, 'search')]",
                                "//*[contains(@resource-id, 'search_button')]",
                                "//*[contains(@resource-id, 'search_icon')]"
                            ]

                            for xpath in search_buttons:
                                try:
                                    search_btn = driver.find_element("xpath", xpath)
                                    if search_btn and search_btn.is_enabled():
                                        search_btn.click()
                                        logger.info(f"✅ 找到并点击了搜索按钮: {xpath}")
                                        await asyncio.sleep(2)
                                        search_success = True
                                        break
                                except:
                                    continue

                        except Exception as btn_e:
                            logger.warning(f"⚠️ 搜索按钮查找失败: {str(btn_e)}")

                        # 方式3：尝试失去焦点再获得焦点
                        if not search_success:
                            logger.info("🔍 尝试失去焦点再获得焦点...")
                            try:
                                # 点击其他地方
                                driver.tap([(500, 300)])
                                await asyncio.sleep(0.5)
                                # 重新点击搜索框
                                search_input.click()
                                await asyncio.sleep(0.5)
                                # 再次尝试回车
                                driver.press_keycode(66)
                                await asyncio.sleep(2)
                                logger.info("✅ 完成失去焦点再获得焦点操作")
                            except Exception as focus_e:
                                logger.warning(f"⚠️ 焦点操作失败: {str(focus_e)}")

                        # 不要假设成功，让最终验证来决定
                    else:
                        logger.warning("⚠️ 无法找到搜索输入框")
                except Exception as e:
                    logger.warning(f"⚠️ 强力搜索触发方式失败: {str(e)}")

            # 方法3：尝试Android特有的搜索方式
            if not search_success:
                try:
                    logger.info("🔍 方法3: 尝试Android特有的搜索方式")

                    # 方式1：尝试发送特殊搜索字符
                    logger.info("⌨️ 尝试发送特殊搜索字符...")
                    try:
                        search_input = await self.element_finder.find_element(driver, "music_search_input")
                        if search_input:
                            # 尝试发送搜索相关的特殊字符
                            search_input.send_keys("\u0084")  # 对应KEYCODE_SEARCH的Unicode
                            await asyncio.sleep(2)
                            logger.info("✅ 尝试了搜索字符")
                    except Exception as search_char_e:
                        logger.warning(f"⚠️ 搜索字符方式失败: {str(search_char_e)}")

                    # 方式2：尝试隐藏键盘然后显示
                    logger.info("⌨️ 尝试隐藏键盘然后显示...")
                    try:
                        driver.hide_keyboard()
                        await asyncio.sleep(1)
                        # 重新点击搜索框
                        search_input = await self.element_finder.find_element(driver, "music_search_input")
                        if search_input:
                            search_input.click()
                            await asyncio.sleep(1)
                            # 尝试发送回车键
                            if hasattr(driver, 'press_key'):
                                driver.press_key(AndroidKey.ENTER)
                                logger.info("✅ 使用press_key发送回车键")
                            else:
                                search_input.send_keys(Keys.ENTER)
                                logger.info("✅ 使用send_keys发送回车键")
                            await asyncio.sleep(2)
                            logger.info("✅ 完成隐藏键盘操作")
                    except Exception as hide_e:
                        logger.warning(f"⚠️ 隐藏键盘操作失败: {str(hide_e)}")

                    # 方式3：尝试多次发送回车键
                    logger.info("⌨️ 尝试多次发送回车键...")
                    try:
                        search_input = await self.element_finder.find_element(driver, "music_search_input")
                        if search_input:
                            # 多次发送回车键
                            for i in range(3):
                                if hasattr(driver, 'press_key'):
                                    driver.press_key(AndroidKey.ENTER)
                                else:
                                    search_input.send_keys(Keys.ENTER)
                                await asyncio.sleep(0.5)
                            logger.info("✅ 尝试了多次回车键")
                    except Exception as multi_enter_e:
                        logger.warning(f"⚠️ 多次回车键失败: {str(multi_enter_e)}")

                    # 方式4：尝试发送特殊字符
                    logger.info("⌨️ 尝试发送特殊字符...")
                    try:
                        search_input = await self.element_finder.find_element(driver, "music_search_input")
                        if search_input:
                            # 尝试发送空格字符（有些搜索框用空格触发搜索）
                            search_input.send_keys(" ")
                            await asyncio.sleep(1)
                            # 删除空格（使用退格键）
                            search_input.send_keys(Keys.BACKSPACE)
                            await asyncio.sleep(1)
                            logger.info("✅ 尝试了空格字符方式")
                    except Exception as space_e:
                        logger.warning(f"⚠️ 空格字符方式失败: {str(space_e)}")

                    # 不要假设成功，让最终验证来决定

                except Exception as e:
                    logger.warning(f"⚠️ Android特有搜索方式失败: {str(e)}")

            # 方法4：最终验证搜索结果
            logger.info("🔍 最终验证搜索结果...")
            await asyncio.sleep(3)  # 给时间让搜索生效

            # 最终检查是否有真实的搜索结果
            final_check_success = False
            try:
                # 检查1：查找包含搜索关键词的元素
                music_elements = driver.find_elements("xpath", f"//*[contains(@text, '{search_keyword}') or contains(@content-desc, '{search_keyword}')]")
                if music_elements and len(music_elements) > 0:
                    logger.info(f"✅ 最终检测到 {len(music_elements)} 个包含搜索关键词的元素")
                    final_check_success = True
                else:
                    # 检查2：查找音乐相关的通用元素
                    music_generic = driver.find_elements("xpath", "//*[contains(@text, 'Last Time') or contains(@text, 'White Color') or contains(@text, 'KSA')]")
                    if music_generic and len(music_generic) > 0:
                        logger.info(f"✅ 最终检测到 {len(music_generic)} 个音乐相关元素")
                        final_check_success = True
                    else:
                        # 检查3：查找任何可能的音乐列表项
                        list_items = driver.find_elements("xpath", "//android.widget.TextView[contains(@text, 'Time') or contains(@text, 'Color')]")
                        if list_items and len(list_items) > 0:
                            logger.info(f"✅ 最终检测到 {len(list_items)} 个可能的音乐列表项")
                            final_check_success = True
                        else:
                            logger.warning("⚠️ 最终未检测到任何音乐搜索结果")

            except Exception as final_check_e:
                logger.warning(f"⚠️ 最终搜索结果检查失败: {str(final_check_e)}")

            # 只有真正检测到搜索结果才认为成功
            if final_check_success:
                search_success = True
                logger.info("🎉 搜索确认成功，找到了搜索结果")
            else:
                search_success = False
                logger.warning("❌ 搜索失败，未找到任何搜索结果")

            logger.info(f"🔍 搜索处理完成，最终状态: {'成功' if search_success else '失败'}")

            # 尝试点击搜索结果
            logger.info("🎵 尝试选择搜索结果")

            # 先尝试通过音乐标题查找
            if music_title:
                music_found = await self.element_finder.find_and_click(
                    driver, "music_item", music_title=music_title
                )
                if music_found:
                    logger.info(f"✅ 成功选择音乐: {music_title}")
                    return True

            # 再尝试通过音乐ID查找
            if music_id:
                music_found = await self.element_finder.find_and_click(
                    driver, "music_item", music_id=music_id
                )
                if music_found:
                    logger.info(f"✅ 成功选择音乐: {music_id}")
                    return True

            logger.warning(f"⚠️ 未找到指定音乐: {search_keyword}")
            return False

        except Exception as e:
            logger.error(f"❌ 音乐选择异常: {str(e)}")
            return False

    async def _execute_handle_music_action(self, driver, step: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """执行音乐处理动作（包括点击添加音效和选择音乐）"""
        try:
            # 从工作流上下文中获取选中的音乐
            selected_music = self.workflow_context.get('selectedMusic', [])

            logger.info(f"🔍 工作流上下文: {list(self.workflow_context.keys())}")
            logger.info(f"🔍 从工作流上下文获取的音乐数据: {selected_music}")

            if not selected_music:
                logger.info("ℹ️ 未选择音乐，跳过整个音乐处理流程")
                return True

            logger.info(f"🎵 检测到选择了 {len(selected_music)} 首音乐，开始音乐处理流程")

            # 步骤1：点击添加音效按钮
            logger.info("🎵 步骤1: 点击添加音效按钮")
            click_success = await self.element_finder.find_and_click(driver, "add_music_button")
            if not click_success:
                logger.error("❌ 无法点击添加音效按钮")
                return False

            await asyncio.sleep(3)  # 等待音乐选择界面加载

            # 步骤2：选择音乐
            logger.info("🎵 步骤2: 选择音乐")

            # 如果选择了多首音乐，随机选择一首
            import random
            if len(selected_music) > 1:
                music = random.choice(selected_music)
                logger.info(f"🎲 从 {len(selected_music)} 首音乐中随机选择了: {music.get('title', '')} ({music.get('music_id', '')})")
            else:
                music = selected_music[0]
                logger.info(f"🎵 选择唯一的音乐: {music.get('title', '')} ({music.get('music_id', '')})")

            music_title = music.get('title', '')
            music_id = music.get('music_id', '')

            logger.info(f"🎵 最终选择的音乐: {music_title} ({music_id})")

            # 点击搜索框
            logger.info("🔍 点击音乐搜索框")
            search_success = await self.element_finder.find_and_click(driver, "music_search_box")
            if not search_success:
                logger.error("❌ 无法点击音乐搜索框")
                return False

            await asyncio.sleep(2)

            # 输入搜索关键词并直接按回车（一步完成）
            search_keyword = music_id if music_id else music_title
            logger.info(f"🔍 搜索音乐: {search_keyword}")

            # 方法1：使用send_keys一步完成输入+回车（推荐）
            search_success = False
            try:
                logger.info("🔍 方法1: 使用send_keys一步完成输入+回车")
                search_input = await self.element_finder.find_element(driver, "music_search_input")
                if search_input:
                    # 先清空输入框
                    search_input.clear()
                    await asyncio.sleep(0.5)
                    # 先输入文本
                    search_input.send_keys(search_keyword)
                    logger.info(f"✅ 成功输入搜索关键词: {search_keyword}")
                    await asyncio.sleep(0.5)

                    # 优先使用ADB发送回车键
                    adb_success = await self._send_enter_key_via_adb(driver, "方法1")

                    # 如果ADB失败，使用Appium方法
                    if not adb_success:
                        logger.info("🔍 ADB方法失败，使用Appium方法发送回车键")
                        try:
                            if hasattr(driver, 'press_key'):
                                driver.press_key(AndroidKey.ENTER)
                                logger.info("✅ 使用press_key发送回车键")
                            else:
                                search_input.send_keys(Keys.ENTER)
                                logger.info("✅ 使用send_keys发送回车键")
                        except Exception as appium_e:
                            logger.warning(f"⚠️ Appium回车键方法也失败: {str(appium_e)}")

                    search_success = True
                else:
                    logger.warning("⚠️ 无法找到搜索输入框")
            except Exception as e:
                logger.warning(f"⚠️ send_keys方法失败: {str(e)}")

            # 方法2：分步输入+回车（备用方案）
            if not search_success:
                try:
                    logger.info("🔍 方法2: 分步输入+回车")
                    input_success = await self.element_finder.input_text(driver, "music_search_input", search_keyword)
                    if input_success:
                        await asyncio.sleep(0.5)
                        # 使用现代方法替代弃用的press_keycode
                        if hasattr(driver, 'press_key'):
                            driver.press_key(AndroidKey.ENTER)
                        else:
                            # 备用方案：直接在元素上发送回车
                            search_input = await self.element_finder.find_element(driver, "music_search_input")
                            if search_input:
                                search_input.send_keys(Keys.ENTER)
                        logger.info("✅ 分步方法成功完成")
                        search_success = True
                    else:
                        logger.warning("⚠️ 分步输入失败")
                except Exception as e:
                    logger.warning(f"⚠️ 分步方法失败: {str(e)}")

            # 方法3：重新聚焦后尝试
            if not search_success:
                try:
                    logger.info("🔍 方法3: 重新聚焦后尝试")
                    # 点击搜索框外的区域
                    driver.tap([(500, 400)])
                    await asyncio.sleep(0.5)
                    # 重新点击搜索框
                    await self.element_finder.find_and_click(driver, "music_search_input")
                    await asyncio.sleep(0.5)
                    # 再次尝试send_keys
                    search_input = await self.element_finder.find_element(driver, "music_search_input")
                    if search_input:
                        search_input.clear()
                        search_input.send_keys(search_keyword + Keys.ENTER)
                        logger.info("✅ 重新聚焦方法成功")
                        search_success = True
                except Exception as e:
                    logger.warning(f"⚠️ 重新聚焦方法失败: {str(e)}")

            if not search_success:
                logger.error("❌ 所有搜索方法都失败了")
                return False

            # 检查搜索是否真的生效
            logger.info("🔍 检查搜索是否生效...")
            await asyncio.sleep(3)  # 等待搜索结果

            # 验证搜索结果 - 更严格的判断
            search_verified = False
            try:
                logger.info("🔍 开始验证搜索结果...")

                # 方法1：检查是否有音乐列表项出现
                music_list_items = driver.find_elements("xpath", "//android.widget.TextView[contains(@text, 'Time') or contains(@text, 'Color') or contains(@text, 'KSA')]")
                logger.info(f"🔍 找到 {len(music_list_items)} 个可能的音乐列表项")

                # 方法2：检查是否有可点击的音乐项
                clickable_music = driver.find_elements("xpath", "//*[@clickable='true' and (contains(@text, 'Time') or contains(@text, 'Color') or contains(@content-desc, 'Time') or contains(@content-desc, 'Color'))]")
                logger.info(f"🔍 找到 {len(clickable_music)} 个可点击的音乐项")

                # 方法3：检查页面源码中是否包含音乐相关信息
                page_source = driver.page_source
                has_music_content = any(keyword in page_source for keyword in ['Last Time', 'White Color', 'KSA012', '播放预览', '音乐'])
                logger.info(f"🔍 页面源码包含音乐内容: {has_music_content}")

                # 方法4：检查是否有音乐播放相关的UI元素
                play_elements = driver.find_elements("xpath", "//*[contains(@content-desc, '播放') or contains(@text, '播放') or contains(@content-desc, 'play') or contains(@text, 'play')]")
                logger.info(f"🔍 找到 {len(play_elements)} 个播放相关元素")

                # 综合判断搜索是否成功
                if music_list_items and len(music_list_items) > 0:
                    logger.info(f"✅ 方法1成功：检测到 {len(music_list_items)} 个音乐列表项")
                    search_verified = True
                elif clickable_music and len(clickable_music) > 0:
                    logger.info(f"✅ 方法2成功：检测到 {len(clickable_music)} 个可点击音乐项")
                    search_verified = True
                elif has_music_content:
                    logger.info("✅ 方法3成功：页面源码包含音乐相关内容")
                    search_verified = True
                elif play_elements and len(play_elements) > 0:
                    logger.info(f"✅ 方法4成功：检测到 {len(play_elements)} 个播放相关元素")
                    search_verified = True
                else:
                    logger.warning("⚠️ 所有验证方法都未检测到搜索结果")

                    # 额外检查：打印当前页面的一些关键信息用于调试
                    try:
                        current_activity = driver.current_activity
                        logger.info(f"🔍 当前活动: {current_activity}")

                        # 查找所有TextView元素，看看页面上有什么
                        all_texts = driver.find_elements("xpath", "//android.widget.TextView")
                        text_contents = [elem.text for elem in all_texts[:10] if elem.text and len(elem.text.strip()) > 0]
                        logger.info(f"🔍 页面前10个文本元素: {text_contents}")

                    except Exception as debug_e:
                        logger.warning(f"⚠️ 调试信息获取失败: {str(debug_e)}")

            except Exception as verify_e:
                logger.warning(f"⚠️ 搜索结果验证失败: {str(verify_e)}")

            # 根据搜索验证结果决定下一步
            if search_verified:
                logger.info("🎉 搜索验证成功，确认找到了搜索结果")
                # 搜索成功，继续选择音乐
                logger.info("🎵 尝试选择搜索结果")
            else:
                logger.error("❌ 搜索验证失败，未找到任何搜索结果")
                logger.info("🔄 尝试其他搜索方法...")

                # 尝试方法2：强力搜索触发
                try:
                    logger.info("🔍 方法2: 尝试强力搜索触发方式")
                    search_input = await self.element_finder.find_element(driver, "music_search_input")
                    if search_input:
                        # 清空并重新输入
                        search_input.clear()
                        await asyncio.sleep(0.5)
                        search_input.send_keys(search_keyword)
                        await asyncio.sleep(1)

                        # 优先使用ADB发送回车键
                        adb_retry_success = await self._send_enter_key_via_adb(driver, "方法2")

                        # 如果ADB失败，使用Appium方法
                        if not adb_retry_success:
                            logger.info("🔍 方法2: ADB失败，使用Appium方法")
                            if hasattr(driver, 'press_key'):
                                driver.press_key(AndroidKey.ENTER)
                                logger.info("✅ 方法2: 使用press_key发送回车键")
                            else:
                                search_input.send_keys(Keys.ENTER)
                                logger.info("✅ 方法2: 使用send_keys发送回车键")

                        await asyncio.sleep(3)

                        # 再次验证搜索结果
                        music_items_retry = driver.find_elements("xpath", "//android.widget.TextView[contains(@text, 'Time') or contains(@text, 'Color') or contains(@text, 'KSA')]")
                        if music_items_retry and len(music_items_retry) > 0:
                            logger.info(f"✅ 方法2成功：检测到 {len(music_items_retry)} 个音乐项")
                            search_verified = True
                        else:
                            logger.warning("⚠️ 方法2也未找到搜索结果")

                except Exception as method2_e:
                    logger.warning(f"⚠️ 方法2失败: {str(method2_e)}")

                # 如果方法2也失败，记录详细信息并继续
                if not search_verified:
                    logger.warning("❌ 所有搜索方法都失败，但继续尝试选择音乐（可能搜索已生效但未检测到）")
                    logger.info("💡 建议：检查搜索关键词是否正确，或者搜索功能是否正常工作")

                logger.info("🎵 尝试选择搜索结果（即使搜索验证失败）")

            # 先尝试通过音乐标题查找
            if music_title:
                logger.info(f"🔍 尝试通过音乐标题查找: {music_title}")
                music_found = await self.element_finder.find_and_click(
                    driver, "music_item", **{"music_title": music_title}
                )
                if music_found:
                    logger.info(f"✅ 成功选择音乐: {music_title}")
                    return True

            # 再尝试通过音乐ID查找
            if music_id:
                logger.info(f"🔍 尝试通过音乐ID查找: {music_id}")
                music_found = await self.element_finder.find_and_click(
                    driver, "music_item", **{"music_id": music_id}
                )
                if music_found:
                    logger.info(f"✅ 成功选择音乐: {music_id}")
                    return True

            # 尝试通过搜索关键词查找
            logger.info(f"🔍 尝试通过搜索关键词查找: {search_keyword}")
            music_found = await self.element_finder.find_and_click(
                driver, "music_item", **{"search_keyword": search_keyword}
            )
            if music_found:
                logger.info(f"✅ 成功选择音乐: {search_keyword}")
                return True

            logger.warning(f"⚠️ 未找到指定音乐: {search_keyword}")
            return False

        except Exception as e:
            logger.error(f"❌ 音乐处理异常: {str(e)}")
            return False

    def _get_step_parameters(self, step: Dict[str, Any]) -> Dict[str, Any]:
        """获取步骤参数"""
        parameters = {}
        for param in step.get('parameters', []):
            param_name = param.get('name')
            if param_name in self.workflow_context:
                parameters[param_name] = self.workflow_context[param_name]
        return parameters

    def _update_progress(self, progress: int, message: str):
        """更新进度"""
        if self.progress_callback:
            self.progress_callback(progress, message)

    def _update_status(self, status: str, message: str):
        """更新状态"""
        if self.status_callback:
            self.status_callback(status, message)
