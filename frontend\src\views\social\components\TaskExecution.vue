<template>
  <div class="task-execution">
    <h2 class="task-title">执行任务</h2>

    <el-row :gutter="20">
      <!-- 任务控制面板 -->
      <el-col :span="8">
        <el-card class="equal-height-card">
          <template #header>
            <div class="card-header">
              <span>任务控制</span>
            </div>
          </template>

          <div class="control-buttons">
            <el-button type="primary" @click="startTask" :disabled="taskStatus === 'running' || taskStatus === 'completed'">
              开始执行
            </el-button>
            <el-button type="warning" @click="pauseTask" :disabled="taskStatus !== 'running'">
              暂停
            </el-button>
            <el-button type="danger" @click="cancelTask" :disabled="taskStatus === 'completed' || taskStatus === 'failed'">
              取消
            </el-button>
          </div>

          <div class="task-info">
            <p><strong>任务ID:</strong> {{ taskId }}</p>
            <p><strong>状态:</strong> <el-tag :type="getStatusType">{{ getStatusText }}</el-tag></p>
            <p><strong>开始时间:</strong> {{ taskInfo.startTime || '未开始' }}</p>
            <p><strong>预计完成时间:</strong> {{ taskInfo.estimatedEndTime || '未知' }}</p>
          </div>
        </el-card>
      </el-col>

      <!-- 执行进度 -->
      <el-col :span="16">
        <el-card class="equal-height-card">
          <template #header>
            <div class="card-header">
              <span>执行进度</span>
              <el-button type="primary" size="small" @click="refreshStatus" :loading="loading.refresh">
                刷新
              </el-button>
            </div>
          </template>

          <el-progress
            :percentage="progress"
            :status="progressStatus"
          />

          <div class="logs-container">
            <el-timeline>
              <el-timeline-item
                v-for="(log, index) in executionLogs"
                :key="index"
                :timestamp="log.time"
                :type="getLogType(log.level)"
              >
                {{ log.message }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务步骤 -->
    <el-row :gutter="20" class="resource-row">
      <el-col :span="24">
        <el-card class="resource-card">
          <template #header>
            <div class="card-header">
              <span>任务执行步骤</span>
            </div>
          </template>

          <el-steps :active="currentStep" finish-status="success" process-status="process">
            <el-step v-for="(step, index) in taskSteps" :key="index" :title="step.name" :description="step.message" :status="step.status" />
          </el-steps>
        </el-card>
      </el-col>
    </el-row>

    <!-- 设备资源监控 -->
    <el-row :gutter="20" class="resource-row">
      <el-col :span="24">
        <el-card class="resource-card">
          <template #header>
            <div class="card-header">
              <span>设备资源监控</span>
            </div>
          </template>

          <el-row :gutter="20">
            <el-col :span="12">
              <h4 class="resource-title">CPU 使用率</h4>
              <el-progress
                :percentage="deviceUsage.cpu"
                :format="formatCpuUsage"
                :stroke-width="15"
              />
            </el-col>
            <el-col :span="12">
              <h4 class="resource-title">内存使用率</h4>
              <el-progress
                :percentage="deviceUsage.memory"
                :status="deviceUsage.memory > 80 ? 'exception' : 'warning'"
                :stroke-width="15"
              />
            </el-col>
          </el-row>

          <el-row :gutter="20" class="network-row">
            <el-col :span="24">
              <h4 class="resource-title">网络状态: {{ deviceUsage.network }}</h4>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { deviceSocketManager } from '@/socket.io'
import { startTaskExecution, pauseTaskExecution, cancelTaskExecution, getTaskStatus, getTaskLogs } from '@/api/social'

const props = defineProps({
  taskId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['execution-completed'])

// 任务状态
const taskStatus = ref('pending') // pending, running, paused, completed, failed
const progress = ref(0)
const socket = ref(null)
const currentStep = ref(1)

// 任务信息
const taskInfo = reactive({
  startTime: '',
  estimatedEndTime: '',
  platform: '',
  account: '',
  contentPath: ''
})

// 任务步骤
const taskSteps = ref([
  { step: 1, name: '准备任务', status: 'success', message: '任务准备就绪' },
  { step: 2, name: '连接设备', status: 'wait', message: '等待任务开始' },
  { step: 3, name: '启动应用', status: 'wait', message: '等待任务开始' },
  { step: 4, name: '执行操作', status: 'wait', message: '等待任务开始' },
  { step: 5, name: '完成任务', status: 'wait', message: '等待任务开始' }
])

// 设备使用情况
const deviceUsage = reactive({
  cpu: 45,
  memory: 70,
  network: '已连接'
})

// 执行日志
const executionLogs = ref([
  { time: '2025-04-14 14:30', message: '任务准备就绪', level: 'info' },
  { time: '2025-04-14 14:31', message: '正在连接设备...', level: 'info' },
  { time: '2025-04-14 14:32', message: '设备连接成功', level: 'success' }
])

// 加载状态
const loading = reactive({
  refresh: false,
  start: false,
  pause: false,
  cancel: false
})

// 计算属性：进度状态
const progressStatus = computed(() => {
  if (taskStatus.value === 'completed') return 'success'
  if (taskStatus.value === 'failed') return 'exception'
  return ''
})

// 计算属性：状态文本
const getStatusText = computed(() => {
  switch (taskStatus.value) {
    case 'pending': return '等待执行'
    case 'running': return '执行中'
    case 'paused': return '已暂停'
    case 'completed': return '已完成'
    case 'failed': return '执行失败'
    default: return '未知状态'
  }
})

// 计算属性：状态类型
const getStatusType = computed(() => {
  switch (taskStatus.value) {
    case 'pending': return 'info'
    case 'running': return 'primary'
    case 'paused': return 'warning'
    case 'completed': return 'success'
    case 'failed': return 'danger'
    default: return 'info'
  }
})

// 初始化
onMounted(async () => {
  await fetchTaskStatus()
  await initWebSocket()
  // 启动定时刷新
  startAutoRefresh()
})

// 清理
onBeforeUnmount(() => {
  stopAutoRefresh()
  closeWebSocket()
})

// 获取任务状态
const fetchTaskStatus = async () => {
  try {
    loading.refresh = true

    // 记录上次状态，用于比较变化
    const previousStatus = taskStatus.value
    const previousProgress = progress.value

    // 调用API获取任务状态
    const response = await getTaskStatus(props.taskId)
    console.log('获取任务状态响应:', response)

    if (!response) {
      console.warn('获取任务状态响应为空')
      return
    }

    // 更新状态
    if (response.status) {
      taskStatus.value = response.status

      // 如果状态发生变化，添加日志
      if (previousStatus !== response.status) {
        addLog(`任务状态变更为: ${response.status}`,
          response.status === 'completed' ? 'success' :
          response.status === 'failed' ? 'error' :
          response.status === 'paused' ? 'warning' : 'info')
      }
    }

    // 更新进度
    if (response.progress !== undefined) {
      progress.value = response.progress

      // 如果进度发生明显变化，添加日志
      if (Math.abs(previousProgress - response.progress) >= 10) {
        addLog(`任务进度: ${response.progress}%`, 'info')
      }
    }

    // 更新任务信息
    if (response.start_time) {
      taskInfo.startTime = response.start_time
    }

    if (response.estimated_end_time) {
      taskInfo.estimatedEndTime = response.estimated_end_time
    }

    // 更新任务步骤
    if (response.steps && response.steps.length > 0) {
      // 更新步骤信息
      response.steps.forEach((step, index) => {
        if (index < taskSteps.value.length) {
          taskSteps.value[index].status = step.status
          taskSteps.value[index].message = step.message
        }
      })
    }

    // 更新当前步骤
    if (response.current_step !== undefined) {
      currentStep.value = response.current_step
    }

    // 更新详细信息
    if (response.details) {
      if (response.details.platform_id) {
        taskInfo.platform = response.details.platform_id
      }
      if (response.details.account_id) {
        taskInfo.account = response.details.account_id
      }
      if (response.details.content_path) {
        taskInfo.contentPath = response.details.content_path
      }
    }

    // 更新设备使用情况
    if (response.device_usage) {
      Object.assign(deviceUsage, response.device_usage)
    }

    // 如果有消息，添加到日志
    if (response.message && response.message !== '任务已开始执行') {
      addLog(response.message,
        response.status === 'completed' ? 'success' :
        response.status === 'failed' ? 'error' :
        response.status === 'paused' ? 'warning' : 'info')
    }

    // 更新日志
    if (response.logs && response.logs.length > 0) {
      // 获取现有日志消息，用于去重
      const existingMessages = new Set(executionLogs.value.map(log => `${log.time}-${log.message}`))

      // 添加新日志（避免重复）
      response.logs.forEach(log => {
        const logTime = log.timestamp || new Date().toLocaleString()
        const logKey = `${logTime}-${log.message}`

        // 如果日志不存在，则添加
        if (!existingMessages.has(logKey)) {
          executionLogs.value.push({
            time: logTime,
            message: log.message,
            level: log.level || 'info'
          })
        }
      })

      // 按时间排序
      executionLogs.value.sort((a, b) => new Date(b.time) - new Date(a.time))

      // 限制日志数量
      if (executionLogs.value.length > 50) {
        executionLogs.value = executionLogs.value.slice(0, 50)
      }
    }

    // 只有在任务成功完成时，才触发完成事件
    if (taskStatus.value === 'completed') {
      emit('execution-completed', {
        status: taskStatus.value,
        taskId: props.taskId
      })

      // 停止自动刷新
      stopAutoRefresh()
    }

    // 如果任务失败或取消，只停止自动刷新，不触发完成事件
    if (taskStatus.value === 'failed' || taskStatus.value === 'canceled' || taskStatus.value === 'partial_completed') {
      // 停止自动刷新
      stopAutoRefresh()

      // 显示提示消息
      if (taskStatus.value === 'failed') {
        ElMessage.error('任务执行失败，请检查日志并修复问题后重试')
      } else if (taskStatus.value === 'canceled') {
        ElMessage.warning('任务已取消')
      } else if (taskStatus.value === 'partial_completed') {
        ElMessage.warning('任务部分完成，有些视频上传失败，请检查日志')
      }
    }

    // 如果任务正在运行，确保自动刷新已启动
    if (taskStatus.value === 'running' && !refreshInterval) {
      startAutoRefresh()
    }

  } catch (error) {
    console.error('获取任务状态失败:', error)
    // 不显示错误消息，避免频繁弹窗
    // ElMessage.error('获取任务状态失败')
    addLog(`获取任务状态失败: ${error.message || error}`, 'error')
  } finally {
    loading.refresh = false
  }
}

// 初始化WebSocket
const initWebSocket = async () => {
  try {
    // 使用设备WebSocket管理器获取Socket实例
    socket.value = await deviceSocketManager.getSocket()

    console.log('WebSocket连接成功')
    addLog('WebSocket连接成功', 'success')

    // 设置重连事件处理
    socket.value.on('reconnect', (attemptNumber) => {
      console.log(`WebSocket重连成功，尝试次数: ${attemptNumber}`)
      addLog(`WebSocket重连成功，尝试次数: ${attemptNumber}`, 'success')

      // 重新订阅任务状态
      if (props.taskId) {
        console.log(`重新订阅任务状态: ${props.taskId}`)
        socket.value.emit('subscribe_task', props.taskId)
      }
    })

    socket.value.on('reconnect_attempt', (attemptNumber) => {
      console.log(`WebSocket尝试重连，次数: ${attemptNumber}`)
      addLog(`WebSocket尝试重连，次数: ${attemptNumber}`, 'warning')
    })

    socket.value.on('reconnect_error', (error) => {
      console.error('WebSocket重连错误:', error)
      addLog('WebSocket重连失败', 'error')
    })

    socket.value.on('reconnect_failed', () => {
      console.error('WebSocket重连失败，已达到最大重试次数')
      addLog('WebSocket重连失败，已达到最大重试次数', 'error')
    })

    // 订阅任务状态更新
    if (props.taskId) {
      console.log(`订阅任务状态: ${props.taskId}`)
      socket.value.emit('subscribe_task', props.taskId)

      // 添加确认回调
      setTimeout(() => {
        // 如果5秒后没有收到任何状态更新，手动刷新一次
        if (executionLogs.value.length <= 1) { // 只有连接成功的日志
          console.log('没有收到任务状态更新，手动刷新')
          fetchTaskStatus()
        }
      }, 5000)
    }

    // 连接错误事件
    socket.value.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error)
      addLog('WebSocket连接失败', 'error')

      // 连接错误时，使用HTTP API获取状态
      fetchTaskStatus()
    })

    // 监听任务状态更新事件
    socket.value.on('task_status_update', (data) => {
      console.log('收到任务状态更新:', data)
      if (data.task_id === props.taskId) {
        updateTaskStatus(data)
        addLog('收到任务状态更新', 'info')
      }
    })

    // 监听设备状态更新事件（可能包含任务相关信息）
    socket.value.on('device_status', (data) => {
      console.log('收到设备状态更新:', data)
      // 如果设备状态包含任务信息，更新任务状态
      if (data.tasks && data.tasks[props.taskId]) {
        updateTaskStatus(data.tasks[props.taskId])
        addLog('从设备状态中获取任务更新', 'info')
      }
    })
  } catch (error) {
    console.error('初始化WebSocket失败:', error)
    addLog('初始化WebSocket失败，将使用HTTP API获取状态', 'error')

    // WebSocket初始化失败时，使用HTTP API获取状态
    fetchTaskStatus()
  }
}

// 关闭WebSocket
const closeWebSocket = () => {
  if (socket.value) {
    socket.value.disconnect()
    socket.value = null
  }
}

// 这部分已移至下方，删除以避免重复定义

// 更新任务状态
const updateTaskStatus = (data) => {
  taskStatus.value = data.status
  progress.value = data.progress || progress.value

  // 更新任务步骤
  if (data.steps && data.steps.length > 0) {
    // 更新步骤信息
    data.steps.forEach((step, index) => {
      if (index < taskSteps.value.length) {
        taskSteps.value[index].status = step.status
        taskSteps.value[index].message = step.message
      }
    })
  }

  // 更新当前步骤
  if (data.current_step !== undefined) {
    currentStep.value = data.current_step
  } else {
    // 根据状态推断当前步骤
    if (taskStatus.value === 'completed') {
      currentStep.value = 5
      taskSteps.value[4].status = 'success'
    } else if (taskStatus.value === 'running') {
      // 查找第一个状态为'process'的步骤
      const processIndex = taskSteps.value.findIndex(step => step.status === 'process')
      if (processIndex >= 0) {
        currentStep.value = processIndex + 1
      } else {
        // 如果没有'process'状态的步骤，设置为第二步
        currentStep.value = 2
        taskSteps.value[1].status = 'process'
      }
    }
  }

  // 更新详细信息
  if (data.details) {
    if (data.details.platform_id) {
      taskInfo.platform = data.details.platform_id
    }
    if (data.details.account_id) {
      taskInfo.account = data.details.account_id
    }
    if (data.details.content_path) {
      taskInfo.contentPath = data.details.content_path
    }
  }

  if (data.logs && data.logs.length > 0) {
    data.logs.forEach(log => {
      addLog(log.message, log.level)
    })
  }

  if (data.device_usage) {
    Object.assign(deviceUsage, data.device_usage)
  }

  if (data.start_time) {
    taskInfo.startTime = data.start_time
  }

  if (data.estimated_end_time) {
    taskInfo.estimatedEndTime = data.estimated_end_time
  }

  // 只有在任务成功完成时，才触发完成事件
  if (taskStatus.value === 'completed') {
    emit('execution-completed', {
      status: taskStatus.value,
      taskId: props.taskId
    })
  }

  // 如果任务失败、取消或部分完成，显示提示消息
  if (taskStatus.value === 'failed' || taskStatus.value === 'canceled' || taskStatus.value === 'partial_completed') {
    // 显示提示消息
    if (taskStatus.value === 'failed') {
      ElMessage.error('任务执行失败，请检查日志并修复问题后重试')
    } else if (taskStatus.value === 'canceled') {
      ElMessage.warning('任务已取消')
    } else if (taskStatus.value === 'partial_completed') {
      ElMessage.warning('任务部分完成，有些视频上传失败，请检查日志')
    }
  }

  // 如果有消息，添加到日志
  if (data.message) {
    addLog(data.message,
      taskStatus.value === 'completed' ? 'success' :
      taskStatus.value === 'failed' ? 'error' :
      taskStatus.value === 'paused' ? 'warning' : 'info')
  }
}

// 添加日志
const addLog = (message, level = 'info') => {
  const now = new Date()
  const timeStr = now.toLocaleString()

  executionLogs.value.unshift({
    time: timeStr,
    message,
    level
  })

  // 限制日志数量
  if (executionLogs.value.length > 50) {
    executionLogs.value.pop()
  }
}

// 获取日志类型
const getLogType = (level) => {
  switch (level) {
    case 'success': return 'success'
    case 'warning': return 'warning'
    case 'error': return 'danger'
    default: return 'info'
  }
}

// 格式化CPU使用率
const formatCpuUsage = () => {
  return `${deviceUsage.cpu}%`
}

// 刷新状态
const refreshStatus = async () => {
  await fetchTaskStatus()
  addLog('手动刷新状态', 'info')
}

// 开始任务
const startTask = async () => {
  try {
    loading.start = true

    // 确认对话框
    await ElMessageBox.confirm('确定要开始执行任务吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    // 确保WebSocket已连接
    if (!socket.value || !socket.value.connected) {
      addLog('WebSocket未连接，尝试重新连接...', 'warning')
      try {
        await initWebSocket()
      } catch (wsError) {
        console.error('WebSocket连接失败:', wsError)
        addLog(`WebSocket连接失败: ${wsError}`, 'error')
      }
    }

    // 调用API开始任务
    addLog('正在开始任务...', 'info')
    const response = await startTaskExecution(props.taskId)
    console.log('开始任务响应:', response)

    // 更新状态
    taskStatus.value = response.status || 'running'

    addLog(response.message || '任务开始执行请求已发送', 'success')
    ElMessage.success('任务已开始执行')

    // 立即刷新状态
    await fetchTaskStatus()

    // 再次刷新状态，确保获取最新状态
    setTimeout(async () => {
      try {
        await fetchTaskStatus()
      } catch (fetchError) {
        console.error('获取任务状态失败:', fetchError)
      }
    }, 3000)

    // 启动自动刷新
    startAutoRefresh()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('开始任务失败:', error)
      ElMessage.error('开始任务失败')
      addLog('开始任务失败', 'error')

      // 尝试获取任务状态
      try {
        await fetchTaskStatus()
      } catch (fetchError) {
        console.error('获取任务状态失败:', fetchError)
      }
    }
  } finally {
    loading.start = false
  }
}

// 暂停任务
const pauseTask = async () => {
  try {
    loading.pause = true

    // 确认对话框
    await ElMessageBox.confirm('确定要暂停任务吗？', '确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用API暂停任务
    const response = await pauseTaskExecution(props.taskId)
    console.log('暂停任务响应:', response)

    // 更新状态
    taskStatus.value = response.status || 'paused'

    addLog(response.message || '任务已暂停', 'warning')
    ElMessage.warning('任务已暂停')

    // 立即刷新状态
    await fetchTaskStatus()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('暂停任务失败:', error)
      ElMessage.error('暂停任务失败')
      addLog('暂停任务失败', 'error')
    }
  } finally {
    loading.pause = false
  }
}

// 取消任务
const cancelTask = async () => {
  try {
    loading.cancel = true

    // 确认对话框
    await ElMessageBox.confirm('确定要取消任务吗？此操作不可恢复！', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用API取消任务
    const response = await cancelTaskExecution(props.taskId)
    console.log('取消任务响应:', response)

    // 更新状态
    taskStatus.value = response.status || 'canceled'

    addLog(response.message || '任务已取消', 'error')
    ElMessage.info('任务已取消')

    // 立即刷新状态
    await fetchTaskStatus()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('取消任务失败:', error)
      ElMessage.error('取消任务失败')
      addLog('取消任务失败', 'error')
    }
  } finally {
    loading.cancel = false
  }
}

// 自动刷新相关
let refreshInterval = null

const startAutoRefresh = () => {
  // 清除之前的定时器
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }

  // 创建新的定时器，每5秒刷新一次
  refreshInterval = setInterval(async () => {
    if (taskStatus.value === 'running') {
      await fetchTaskStatus()
    } else if (taskStatus.value === 'completed' || taskStatus.value === 'failed' || taskStatus.value === 'canceled' || taskStatus.value === 'partial_completed') {
      // 如果任务已完成、失败、取消或部分完成，停止自动刷新
      stopAutoRefresh()
    }
  }, 5000)
}

const stopAutoRefresh = () => {
  if (refreshInterval) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
}
</script>

<style scoped>
.task-execution {
  padding: 10px;
  height: 100%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.task-title {
  margin-top: 0;
  margin-bottom: 10px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.control-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.task-info {
  margin-top: 15px;
  line-height: 1.5;
}

.task-info p {
  margin: 5px 0;
}

.resource-row {
  margin-top: 10px;
}

.network-row {
  margin-top: 10px;
}

.equal-height-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.equal-height-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.logs-container {
  flex: 1;
  overflow-y: auto;
  padding-right: 10px;
  margin-top: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #f9f9f9;
  max-height: 250px; /* 减小最大高度，使页面更紧凑 */
}

/* 自定义滚动条样式 */
.logs-container::-webkit-scrollbar {
  width: 6px;
}

.logs-container::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.logs-container::-webkit-scrollbar-track {
  background-color: #f2f6fc;
}

.resource-card {
  margin-bottom: 10px;
}

.resource-title {
  margin: 5px 0;
  font-size: 14px;
}
</style>
