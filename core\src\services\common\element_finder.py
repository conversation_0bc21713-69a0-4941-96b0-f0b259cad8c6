"""
通用元素查找器
支持配置文件驱动的元素定位和操作
"""

import asyncio
import logging
import yaml
from typing import Dict, List, Any, Optional
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from appium.webdriver.common.appiumby import AppiumBy

logger = logging.getLogger(__name__)


class ElementFinder:
    """配置驱动的元素查找器"""

    def __init__(self, config_path: str):
        """初始化元素查找器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.elements = self.config.get('elements', {})
        self.wait_times = self.config.get('wait_times', {})
        self.retry_config = self.config.get('retry_config', {})

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            logger.info(f"尝试加载配置文件: {self.config_path}")
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                logger.info(f"✅ 成功加载元素配置: {self.config_path}")
                logger.info(f"配置文件包含的元素: {list(config.get('elements', {}).keys())}")
                return config
        except Exception as e:
            logger.error(f"❌ 加载配置文件失败: {self.config_path}, 错误: {e}")
            return {}

    async def find_and_click(self, driver, element_name: str, **kwargs) -> bool:
        """查找并点击元素

        Args:
            driver: Appium驱动
            element_name: 元素名称
            **kwargs: 模板参数（如filename等）

        Returns:
            bool: 是否成功点击
        """
        if element_name not in self.elements:
            logger.error(f"配置中未找到元素: {element_name}")
            return False

        element_configs = self.elements[element_name]
        logger.info(f"🔍 查找并点击元素: {element_name}")

        for i, config in enumerate(element_configs, 1):
            try:
                logger.info(f"🔍 方法{i}：{config.get('description', config['type'])}")

                element = await self._find_element(driver, config, **kwargs)
                if element:
                    # 检查是否是坐标点击（已经执行了点击操作）
                    if element == "coordinate_clicked":
                        logger.info(f"✅ 成功点击 {element_name} (方法{i}: {config['type']} - 坐标点击)")
                    else:
                        # 普通元素点击
                        element.click()
                        logger.info(f"✅ 成功点击 {element_name} (方法{i}: {config['type']})")

                    # 等待点击后的延迟
                    wait_time = self.wait_times.get('after_click', 2)
                    await asyncio.sleep(wait_time)
                    return True

            except Exception as e:
                logger.warning(f"❌ 方法{i}失败 ({config['type']}): {str(e)}")
                continue

        logger.error(f"❌ 所有方法都失败: {element_name}")
        return False

    async def find_element(self, driver, element_name: str, wait_for_real_element: bool = False, **kwargs):
        """仅查找元素，不执行操作

        Args:
            driver: Appium驱动
            element_name: 元素名称
            wait_for_real_element: 是否只查找真实元素（跳过坐标定位）
            **kwargs: 模板参数

        Returns:
            WebElement or None: 找到的元素
        """
        if element_name not in self.elements:
            logger.error(f"配置中未找到元素: {element_name}")
            return None

        element_configs = self.elements[element_name]
        logger.info(f"🔍 查找元素: {element_name}")

        for i, config in enumerate(element_configs, 1):
            try:
                # 如果是等待真实元素，跳过坐标定位
                if wait_for_real_element and config['type'] in ['coordinate', 'coordinate_relative']:
                    logger.debug(f"⏭️ 跳过坐标定位方法{i}：{config.get('description', config['type'])}")
                    continue

                logger.debug(f"🔍 方法{i}：{config.get('description', config['type'])}")
                element = await self._find_element(driver, config, **kwargs)
                if element and element != "coordinate_clicked":
                    # 额外检查元素是否真实可见和可点击
                    if wait_for_real_element:
                        try:
                            is_displayed = element.is_displayed()
                            is_enabled = element.is_enabled()
                            if is_displayed and is_enabled:
                                logger.info(f"✅ 找到真实可见元素 {element_name} (方法{i}: {config['type']})")
                                return element
                            else:
                                logger.debug(f"⚠️ 元素存在但不可见或不可用: displayed={is_displayed}, enabled={is_enabled}")
                                continue
                        except Exception as check_error:
                            logger.debug(f"⚠️ 检查元素可见性失败: {str(check_error)}")
                            continue
                    else:
                        logger.info(f"✅ 找到元素 {element_name} (方法{i}: {config['type']})")
                        return element

            except Exception as e:
                logger.debug(f"❌ 方法{i}失败 ({config['type']}): {str(e)}")
                continue

        logger.error(f"❌ 未找到元素: {element_name}")
        return None

    async def _find_element(self, driver, config: Dict[str, Any], **kwargs):
        """根据配置查找单个元素

        Args:
            driver: Appium驱动
            config: 元素配置
            **kwargs: 模板参数

        Returns:
            WebElement or None: 找到的元素
        """
        element_type = config['type']
        value = config['value']
        timeout = config.get('timeout', 10)

        # 处理模板参数
        if isinstance(value, str) and '{' in value:
            try:
                value = value.format(**kwargs)
            except KeyError as e:
                logger.warning(f"模板参数缺失: {e}")
                return None

        try:
            if element_type == "id":
                # 先尝试找到可点击的元素
                try:
                    return WebDriverWait(driver, timeout).until(
                        EC.element_to_be_clickable((AppiumBy.ID, value))
                    )
                except:
                    # 如果元素不可点击，尝试找到元素并查找其可点击的父元素
                    try:
                        element = WebDriverWait(driver, timeout).until(
                            EC.presence_of_element_located((AppiumBy.ID, value))
                        )
                        return self._find_clickable_parent(element)
                    except:
                        raise
            elif element_type == "xpath" or element_type == "xpath_template":
                return WebDriverWait(driver, timeout).until(
                    EC.element_to_be_clickable((AppiumBy.XPATH, value))
                )
            elif element_type == "accessibility_id":
                return WebDriverWait(driver, timeout).until(
                    EC.element_to_be_clickable((AppiumBy.ACCESSIBILITY_ID, value))
                )
            elif element_type == "coordinate":
                # 坐标点击不需要查找元素，直接执行点击
                driver.tap([value])
                return "coordinate_clicked"  # 返回特殊标识表示坐标点击已完成
            elif element_type == "coordinate_relative":
                # 相对坐标点击
                screen_size = driver.get_window_size()
                x = int(screen_size['width'] * value[0])
                y = int(screen_size['height'] * value[1])
                driver.tap([(x, y)])
                return "coordinate_clicked"  # 返回特殊标识表示坐标点击已完成
            else:
                logger.error(f"不支持的元素类型: {element_type}")
                return None

        except Exception as e:
            logger.debug(f"查找元素失败 ({element_type}): {str(e)}")
            return None

    async def input_text(self, driver, element_name: str, text: str) -> bool:
        """在指定元素中输入文本

        Args:
            driver: Appium驱动
            element_name: 元素名称
            text: 要输入的文本

        Returns:
            bool: 是否成功输入
        """
        element = await self.find_element(driver, element_name)
        if element:
            try:
                element.clear()
                element.send_keys(text)
                logger.info(f"✅ 成功输入文本到 {element_name}: {text}")
                return True
            except Exception as e:
                logger.error(f"❌ 输入文本失败 {element_name}: {str(e)}")
                return False
        return False

    def get_wait_time(self, wait_type: str) -> float:
        """获取等待时间

        Args:
            wait_type: 等待类型

        Returns:
            float: 等待时间（秒）
        """
        return self.wait_times.get(wait_type, 2.0)

    def _find_clickable_parent(self, element):
        """查找可点击的父元素

        Args:
            element: 当前元素

        Returns:
            WebElement: 可点击的父元素，如果找不到则返回None
        """
        try:
            current = element
            max_levels = 3  # 最多向上查找3级父元素

            for level in range(max_levels):
                if current.get_attribute("clickable") == "true":
                    logger.info(f"找到可点击的父元素（第{level}级）")
                    return current

                # 查找父元素
                try:
                    current = current.find_element(AppiumBy.XPATH, "./..")
                except:
                    logger.warning(f"无法找到第{level+1}级父元素")
                    break

            logger.warning("未找到可点击的父元素")
            return None

        except Exception as e:
            logger.error(f"查找可点击父元素异常: {str(e)}")
            return None
