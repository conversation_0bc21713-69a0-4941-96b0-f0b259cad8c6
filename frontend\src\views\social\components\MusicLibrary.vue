<template>
  <div class="music-library">
    <el-card class="music-card">
      <template #header>
        <div class="card-header">
          <span>🎵 音乐库选择</span>
          <el-button type="primary" size="small" @click="showAddDialog = true">
            添加音乐
          </el-button>
        </div>
      </template>

      <!-- 搜索和筛选 -->
      <div class="search-section">
        <el-row :gutter="16">
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索音乐标题或ID"
              @input="handleSearch"
              clearable
            >
              <template #prefix>
                <el-icon><Search /></el-icon>
              </template>
            </el-input>
          </el-col>
          <el-col :span="6">
            <el-select v-model="selectedCategory" placeholder="选择分类" @change="loadMusicList" clearable>
              <el-option
                v-for="category in categories"
                :key="category"
                :label="category"
                :value="category"
              />
            </el-select>
          </el-col>
          <el-col :span="10">
            <el-select
              v-model="selectedTags"
              placeholder="选择标签"
              multiple
              @change="loadMusicList"
              clearable
            >
              <el-option
                v-for="tag in tags"
                :key="tag"
                :label="tag"
                :value="tag"
              />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 音乐列表 -->
      <div class="music-list">
        <el-table
          :data="musicList"
          @selection-change="handleSelectionChange"
          style="width: 100%"
          max-height="400"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="music_id" label="音乐ID" width="120" />
          <el-table-column prop="title" label="标题" min-width="150" />
          <el-table-column prop="duration" label="时长" width="80" />
          <el-table-column prop="category" label="分类" width="100" />
          <el-table-column prop="tags" label="标签" min-width="150">
            <template #default="scope">
              <el-tag
                v-for="tag in scope.row.tags"
                :key="tag"
                size="small"
                style="margin-right: 5px;"
              >
                {{ tag }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <!-- 选中的音乐 -->
      <div v-if="selectedMusic.length > 0" class="selected-music">
        <el-divider content-position="left">已选择的音乐</el-divider>
        <div class="selected-items">
          <el-tag
            v-for="music in selectedMusic"
            :key="music.music_id"
            closable
            @close="removeSelectedMusic(music)"
            style="margin-right: 10px; margin-bottom: 5px;"
          >
            {{ music.title }} ({{ music.music_id }})
          </el-tag>
        </div>
      </div>
    </el-card>

    <!-- 添加音乐对话框 -->
    <el-dialog v-model="showAddDialog" title="添加音乐" width="600px">
      <el-form :model="newMusic" :rules="musicRules" ref="musicFormRef" label-width="80px">
        <el-form-item label="音乐ID" prop="music_id">
          <el-input v-model="newMusic.music_id" placeholder="如：KSA012316484" />
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="newMusic.title" placeholder="如：Last Time" />
        </el-form-item>
        <el-form-item label="时长" prop="duration">
          <el-input v-model="newMusic.duration" placeholder="如：2:30" />
        </el-form-item>
        <el-form-item label="分类" prop="category">
          <el-input v-model="newMusic.category" placeholder="如：背景音乐" />
        </el-form-item>
        <el-form-item label="标签" prop="tags">
          <el-select
            v-model="newMusic.tags"
            multiple
            filterable
            allow-create
            placeholder="输入标签，如：平静、梦幻"
            style="width: 100%"
          >
            <el-option
              v-for="tag in tags"
              :key="tag"
              :label="tag"
              :value="tag"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="addMusic" :loading="adding">添加</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { musicApi } from '@/api/music'

// Props
const props = defineProps({
  platform: {
    type: String,
    default: 'youtube'
  }
})

// Emits
const emit = defineEmits(['music-selected'])

// 响应式数据
const musicList = ref([])
const categories = ref([])
const tags = ref([])
const selectedMusic = ref([])
const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedTags = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)
const loading = ref(false)

// 添加音乐对话框
const showAddDialog = ref(false)
const adding = ref(false)
const musicFormRef = ref()
const newMusic = reactive({
  music_id: '',
  title: '',
  duration: '',
  category: '背景音乐',
  tags: [],
  platform: props.platform
})

// 表单验证规则
const musicRules = {
  music_id: [
    { required: true, message: '请输入音乐ID', trigger: 'blur' }
  ],
  title: [
    { required: true, message: '请输入音乐标题', trigger: 'blur' }
  ]
}

// 方法
const loadMusicList = async () => {
  loading.value = true
  try {
    const params = {
      platform: props.platform,
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    }

    if (searchKeyword.value) {
      params.search = searchKeyword.value
    }
    if (selectedCategory.value) {
      params.category = selectedCategory.value
    }
    if (selectedTags.value.length > 0) {
      params.tags = selectedTags.value.join(',')
    }

    const response = await musicApi.getMusicLibrary(params)
    console.log('🎵 获取音乐库响应:', response)

    // 处理不同的响应格式
    const responseData = response.success ? response.data : response
    if (responseData.code === 200) {
      musicList.value = responseData.data.items
      total.value = responseData.data.total
    }
  } catch (error) {
    ElMessage.error('加载音乐库失败')
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const response = await musicApi.getCategories(props.platform)
    console.log('📂 获取分类响应:', response)

    const responseData = response.success ? response.data : response
    if (responseData.code === 200) {
      categories.value = responseData.data.categories
    }
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

const loadTags = async () => {
  try {
    const response = await musicApi.getTags(props.platform)
    console.log('🏷️ 获取标签响应:', response)

    const responseData = response.success ? response.data : response
    if (responseData.code === 200) {
      tags.value = responseData.data.tags
    }
  } catch (error) {
    console.error('加载标签失败:', error)
  }
}

const handleSearch = () => {
  currentPage.value = 1
  loadMusicList()
}

const handleSelectionChange = (selection) => {
  selectedMusic.value = selection
  emit('music-selected', selection)
}

const removeSelectedMusic = (music) => {
  const index = selectedMusic.value.findIndex(item => item.music_id === music.music_id)
  if (index > -1) {
    selectedMusic.value.splice(index, 1)
    emit('music-selected', selectedMusic.value)
  }
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadMusicList()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadMusicList()
}

const addMusic = async () => {
  console.log('🎵 addMusic 方法被调用')
  console.log('📋 表单引用:', musicFormRef.value)
  console.log('📋 表单数据:', newMusic)

  if (!musicFormRef.value) {
    console.error('❌ 表单引用不存在')
    ElMessage.error('表单引用错误')
    return
  }

  await musicFormRef.value.validate(async (valid) => {
    console.log('📋 表单验证结果:', valid)

    if (valid) {
      adding.value = true
      console.log('🚀 开始发送请求...')

      try {
        console.log('📤 发送的数据:', newMusic)
        const response = await musicApi.createMusic(newMusic)
        console.log('📥 API响应:', response)

        // 处理不同的响应格式
        const responseData = response.success ? response.data : response
        console.log('📥 处理后的响应数据:', responseData)

        if (responseData.code === 200) {
          ElMessage.success('添加音乐成功')
          showAddDialog.value = false
          // 重置表单
          Object.assign(newMusic, {
            music_id: '',
            title: '',
            duration: '',
            category: '背景音乐',
            tags: [],
            platform: props.platform
          })
          // 重新加载列表
          loadMusicList()
          loadTags() // 重新加载标签
        } else {
          console.error('❌ API返回错误:', responseData)
          ElMessage.error(responseData.message || '添加音乐失败')
        }
      } catch (error) {
        console.error('❌ 请求异常:', error)
        ElMessage.error(`添加音乐失败: ${error.message}`)
      } finally {
        adding.value = false
      }
    } else {
      console.error('❌ 表单验证失败')
    }
  })
}

// 监听平台变化
watch(() => props.platform, () => {
  loadMusicList()
  loadCategories()
  loadTags()
})

// 初始化
onMounted(() => {
  loadMusicList()
  loadCategories()
  loadTags()
})
</script>

<style scoped>
.music-library {
  margin-top: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section {
  margin-bottom: 20px;
}

.music-list {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.selected-music {
  margin-top: 20px;
}

.selected-items {
  margin-top: 10px;
}
</style>
