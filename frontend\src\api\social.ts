import request from '@/utils/request'
import type { SocialAccount, SocialApp, SocialPost, SocialPlatform, PlatformApp } from '@/types/social'

// 获取社媒应用列表
export const getSocialApps = () => {
  return request<SocialApp[]>({
    url: '/api/social/apps',
    method: 'get'
  })
}

// 获取平台列表
export const getPlatforms = (status?: string) => {
  const params = status ? { status } : {}
  return request<SocialPlatform[]>({
    url: '/api/v1/social/platforms',
    method: 'get',
    params
  })
}

// 获取单个平台
export const getPlatform = (id: string) => {
  return request<SocialPlatform>({
    url: `/api/v1/social/platforms/${id}`,
    method: 'get'
  })
}

// 创建平台
export const createPlatform = (data: Omit<SocialPlatform, '_id'>) => {
  return request<SocialPlatform>({
    url: '/api/v1/social/platforms',
    method: 'post',
    data
  })
}

// 更新平台
export const updatePlatform = (id: string, data: Partial<SocialPlatform>) => {
  return request<SocialPlatform>({
    url: `/api/v1/social/platforms/${id}`,
    method: 'put',
    data
  })
}

// 删除平台
export const deletePlatform = (id: string) => {
  return request<{ deleted: boolean }>({
    url: `/api/v1/social/platforms/${id}`,
    method: 'delete'
  })
}

// 获取平台应用列表
export const getPlatformApps = (platformId: string, type?: string) => {
  const params = type ? { type } : {}
  return request<PlatformApp[]>({
    url: `/api/v1/social/platforms/${platformId}/apps`,
    method: 'get',
    params
  })
}

// 获取账号列表（通用）
export interface AccountsResponse {
  data: SocialAccount[]
  total: number
  page: number
  page_size: number
}

export const getAccounts = (params?: {
  platform_id?: string
  core_service_id?: string
  status?: string
  skip?: number
  limit?: number
  app_id?: string
}) => {
  return request<AccountsResponse>({
    url: '/api/v1/social/accounts',
    method: 'get',
    params
  })
}

// 添加账号
export const addAccount = (data: Omit<SocialAccount, 'id'>) => {
  return request<SocialAccount>({
    url: '/api/v1/social/accounts',
    method: 'post',
    data
  })
}

// 创建账号（新API）
export const createAccount = (data: Omit<SocialAccount, 'id'>) => {
  return request<SocialAccount>({
    url: '/api/v1/social/accounts',
    method: 'post',
    data
  })
}

// 更新单个账号
export const updateAccount = (id: string, data: Partial<SocialAccount>) => {
  return request<SocialAccount>({
    url: `/api/v1/social/accounts/${id}`,
    method: 'put',
    data
  })
}

// 发布内容
export const createPost = (data: Omit<SocialPost, 'id'>) => {
  return request<SocialPost>({
    url: '/api/social/posts',
    method: 'post',
    data
  })
}

export interface SocialAnalyticsItem {
  _id?: {
    platform?: string
    date?: string
  }
  likes?: number
  comments?: number
  shares?: number
  views?: number
  [key: string]: any
}

export interface SocialAnalyticsResponse {
  data: SocialAnalyticsItem[]
  summary: {
    total_likes: number
    total_comments: number
    total_shares: number
    total_views: number
  }
}

// 获取分析数据
export const getAnalytics = (params: {
  appId?: string
  accountId?: string
  days?: number
  platform?: string
}) => {
  return request<SocialAnalyticsResponse>({
    url: '/api/social/analytics',
    method: 'get',
    params
  })
}

// 获取内容列表
export const getPosts = (appId: string) => {
  return request<SocialPost[]>({
    url: `/api/social/posts?app_id=${appId}`,
    method: 'get'
  })
}

// 控制社媒应用
export const controlApp = (data: {
  deviceId: string
  appId: string
  action: 'start' | 'stop' | 'clear'
}) => {
  return request({
    url: '/api/social/control',
    method: 'post',
    data
  })
}

// 删除单个账号
export const deleteAccount = (id: string) => {
  return request<{ deleted: boolean }>({
    url: `/api/v1/social/accounts/${id}`,
    method: 'delete'
  })
}

// 批量删除账号
export const batchDeleteAccounts = (accountIds: string[]) => {
  return request<{ deleted_count: number }>({
    url: '/api/v1/social/accounts/batch_delete',
    method: 'post',
    data: {
      account_ids: accountIds
    }
  })
}

// 批量更新账号
export const batchUpdateAccounts = (accountIds: string[], updateData: Partial<SocialAccount>) => {
  return request<{ updated_count: number }>({
    url: '/api/v1/social/accounts/batch_update',
    method: 'post',
    data: {
      account_ids: accountIds,
      update_data: updateData
    }
  })
}

// 导入账号
export interface ImportAccountsResponse {
  imported_count: number;
  errors: string[];
  [key: string]: any;
}

export const importAccounts = (
  textContent: string,
  platformMapping?: Record<string, string>,
  coreServiceId?: string,
  format: 'text' | 'csv' = 'text'
) => {
  return request<ImportAccountsResponse>({
    url: '/api/v1/social/accounts/import',
    method: 'post',
    data: {
      text_content: textContent,
      platform_mapping: platformMapping,
      core_service_id: coreServiceId,
      format: format
    }
  })
}

// 导出账号
export const exportAccounts = (params?: {
  platform_id?: string
  core_service_id?: string
  format?: 'csv' | 'json'
  account_ids?: string[] // 添加账号ID列表参数
}) => {
  // 如果有账号ID列表，使用POST方法，否则使用GET方法
  if (params?.account_ids && params.account_ids.length > 0) {
    return request({
      url: '/api/v1/social/accounts/export',
      method: 'post',
      data: {
        account_ids: params.account_ids,
        format: params.format || 'csv'
      },
      responseType: params?.format === 'json' ? 'json' : 'blob'
    })
  } else {
    return request({
      url: '/api/v1/social/accounts/export',
      method: 'get',
      params,
      responseType: params?.format === 'json' ? 'json' : 'blob'
    })
  }
}

export interface PublishTask {
  platform_id: string
  account_id: string
  content_path: string
  schedule_type?: 'immediate' | 'scheduled'
  schedule_time?: string
  workflow_id?: string // 添加工作流ID字段
}

export interface Workflow {
  id: string
  name: string
  description: string
  created_at: string
}

// 修改工作流API路径，使用正确的API版本
export const getWorkflows = () => {
  return request<Workflow[]>({
    // 修改为可能存在的API路径
    url: '/api/social/workflows', // 从v1改为social命名空间
    method: 'get'
  })
}

export const createTask = (data: PublishTask) => {
  return request({
    url: '/api/social/tasks',
    method: 'post',
    data
  })
}

export interface ValidateFolderResponse {
  valid: boolean
  files: Array<{
    name: string
    size: number
    type: string
    valid: boolean
  }>
}

export const validateFolder = (data: { path: string }) => {
  return request({
    url: '/api/v1/social/validate-folder',
    method: 'post',
    data
  })
}

// 获取特定平台账号列表（重命名以避免冲突）
export const getPlatformAccounts = (platform: string) => {
  return request({
    url: `/api/v1/social/${platform}/accounts`,
    method: 'get'
  })
}

// 创建上传任务
export const createUploadTask = (data: {
  folderPath: string
  accountId: string
  metadata: {
    titleTemplate: string
    description: string
    tags: string[]
    privacyStatus: string
    contentType: string  // 新增内容类型字段
    musicLibrary?: string
    publishStrategy?: string
  }
}) => {
  return request({
    url: '/api/v1/social/youtube/uploads',
    method: 'post',
    data
  })
}

// 获取上传任务列表
export const getUploadTasks = () => {
  return request({
    url: '/api/v1/social/youtube/uploads',
    method: 'get'
  })
}

// 获取任务日志
export const getTaskLogs = (taskId: string) => {
  return request({
    url: `/api/social/tasks/${taskId}/logs`,
    method: 'get'
  })
}

// 获取任务状态
export const getTaskStatus = (taskId: string) => {
  return request({
    url: `/api/social/tasks/${taskId}/status`,
    method: 'get'
  }).then(response => {
    // 确保返回的是一个对象，而不是Promise
    return response.data || { status: 'unknown', message: '无法获取任务状态' }
  }).catch(error => {
    console.error('获取任务状态失败:', error)
    throw error
  })
}

// 更新任务配置
export const updateTaskConfig = (taskId: string, config: any) => {
  return request({
    url: `/api/social/tasks/${taskId}/config`,
    method: 'put',
    data: config
  })
}

// 开始执行任务
export const startTaskExecution = (taskId: string) => {
  return request({
    url: `/api/social/tasks/${taskId}/start`,
    method: 'post'
  }).then(response => {
    // 确保返回的是一个对象，而不是Promise
    return response.data || { status: 'running', message: '任务已开始执行' }
  }).catch(error => {
    console.error('开始任务执行失败:', error)
    throw error
  })
}

// 暂停任务
export const pauseTaskExecution = (taskId: string) => {
  return request({
    url: `/api/social/tasks/${taskId}/pause`,
    method: 'post'
  }).then(response => {
    // 确保返回的是一个对象，而不是Promise
    return response.data || { status: 'paused', message: '任务已暂停' }
  }).catch(error => {
    console.error('暂停任务失败:', error)
    throw error
  })
}

// 取消任务
export const cancelTaskExecution = (taskId: string) => {
  return request({
    url: `/api/social/tasks/${taskId}/cancel`,
    method: 'post'
  }).then(response => {
    // 确保返回的是一个对象，而不是Promise
    return response.data || { status: 'canceled', message: '任务已取消' }
  }).catch(error => {
    console.error('取消任务失败:', error)
    throw error
  })
}

// 文件系统API
export interface FileInfo {
  name: string
  path: string
  size: number
  is_directory: boolean
  extension?: string
  last_modified?: string
}

export interface FolderListResponse {
  path: string
  files: FileInfo[]
  parent_path?: string
}

// 获取文件夹内容
export const listFolderContents = (path: string, filterExtensions?: string[]) => {
  return request<FolderListResponse>({
    url: '/api/v1/filesystem/list',
    method: 'post',
    data: { path },
    params: filterExtensions ? { filter_extensions: filterExtensions } : {}
  })
}

// 获取驱动器列表
export const listDrives = () => {
  return request<string[]>({
    url: '/api/v1/filesystem/drives',
    method: 'get'
  })
}

// 验证视频文件夹
export const validateVideoFolder = (path: string) => {
  return request({
    url: '/api/v1/filesystem/validate-video-folder',
    method: 'post',
    data: { path }
  })
}

// 获取Core服务列表
export const getCoreServices = () => {
  return request({
    url: '/api/v1/cores',
    method: 'get'
  })
}

// 获取特定平台和Core服务的账号列表
export const getServiceAccounts = (platformId: string, coreServiceId: string) => {
  return request({
    url: `/api/v1/social/accounts`,
    method: 'get',
    params: {
      platform_id: platformId,
      core_service_id: coreServiceId
    }
  })
}

// 获取平台发布路径
export const listPlatformPaths = (platformId: string, coreServiceId: string, accountId: string) => {
  console.log('调用listPlatformPaths API:', {
    platformId,
    coreServiceId,
    accountId
  })

  return request({
    url: '/api/v1/social/publish-paths',
    method: 'get',
    params: {
      platform_id: platformId,
      core_service_id: coreServiceId,
      account_id: accountId
    }
  }).then(response => {
    console.log('listPlatformPaths API响应:', response)
    return response
  }).catch(error => {
    console.error('listPlatformPaths API错误:', error)
    throw error
  })
}

// 上传平台图标
export const uploadPlatformIcon = (file: File, platformId?: string) => {
  const formData = new FormData()
  formData.append('file', file)

  // 如果提供了平台ID，添加到表单数据中
  if (platformId) {
    formData.append('platform_id', platformId)
    console.log('上传图标，添加平台ID到表单:', platformId)
  }

  // 打印完整的表单数据，帮助调试
  console.log('上传图标表单数据:')
  try {
    // 使用any类型避免TypeScript错误
    const entries = (formData as any).entries()
    for (const pair of entries) {
      console.log(`${pair[0]}: ${pair[1]}`)
    }
  } catch (e) {
    console.log('无法打印表单数据:', e)
  }

  return request({
    url: '/api/v1/social/platforms/upload-icon',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // 添加超时设置，图片上传可能需要更长时间
    timeout: 30000
  })
}

// 设备账号关联API

// 获取设备关联的账号
export const getDeviceAccounts = (deviceId: string, platformId?: string) => {
  const params = platformId ? { platform_id: platformId } : {}
  return request({
    url: `/api/v1/social/devices/${deviceId}/accounts`,
    method: 'get',
    params
  })
}

// 获取账号关联的设备
export const getAccountDevices = (accountId: string) => {
  console.log('获取账号关联的设备:', accountId);

  // 使用_id作为参数名，并确保URL中的accountId和params中的_id都是一样的
  return request({
    url: `/api/v1/social/accounts/${accountId}/devices`,
    method: 'get',
    params: {
      _id: accountId,  // 添加_id参数
      account_id: accountId  // 添加account_id参数
    }
  })
}

// 关联设备和账号
export const linkDeviceAccount = (data: {
  device_id: string
  account_id: string
  platform_id: string
  app_id: string
  settings?: {
    auto_login?: boolean
    keep_alive?: boolean
    notification?: boolean
  }
}) => {
  console.log('关联设备和账号:', data);

  return request({
    url: `/api/v1/social/devices/${data.device_id}/accounts`,
    method: 'post',
    data: {
      account_id: data.account_id,
      platform_id: data.platform_id,
      app_id: data.app_id,
      settings: data.settings
    }
  })
}

// 解除设备和账号关联
export const unlinkDeviceAccount = (deviceId: string, accountId: string) => {
  console.log('解除设备和账号关联:', deviceId, accountId);

  return request({
    url: `/api/v1/social/devices/${deviceId}/accounts/${accountId}`,
    method: 'delete'
  })
}


